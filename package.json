{"scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject", "format": "prettier --write '**/*.js'", "lint:fix": "eslint . --ext .js --fix", "postinstall": "patch-package", "build:dev": "eas build --profile development", "build:dev:android": "eas build --profile development -p android", "build:dev:ios": "eas build --profile development -p ios", "start:dev-client": "expo start --dev-client", "build:local:android": "cd android && ./gradlew assembleDebug", "build:local:release:android": "cd android && ./gradlew assembleRelease", "build:staging": "eas build --profile staging -p all", "build:staging:android": "eas build --profile staging -p android", "build:staging:ios": "eas build --profile staging -p ios", "build:production": "eas build --profile production -p all", "build:production:android": "eas build --profile production -p android", "build:production:ios": "eas build --profile production -p ios", "submit:production": "eas submit --profile production -p all", "submit:production:android": "eas submit --profile production -p android", "submit:production:ios": "eas submit --profile production -p ios", "publish:staging": "ENVIRONMENT=staging expo publish --release-channel staging", "publish:production": "ENVIRONMENT=production expo publish --release-channel production"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["npm run format", "npm run lint:fix"]}, "dependencies": {"@apollo/client": "^3.5.8", "@expo/config-plugins": "~6.0.0", "@expo/webpack-config": "^18.0.1", "@react-native-async-storage/async-storage": "1.17.11", "@react-native-community/masked-view": "0.1.11", "@react-navigation/bottom-tabs": "^6.2.0", "@react-navigation/drawer": "^6.3.1", "@react-navigation/material-top-tabs": "^5.3.19", "@react-navigation/native": "^6.0.8", "@react-navigation/native-stack": "^6.5.0", "@sentry/react-native": "4.15.2", "@use-expo/font": "^2.0.0", "expo": "~48.0.21", "expo-av": "~13.2.1", "expo-constants": "~14.2.1", "expo-device": "~5.2.1", "expo-localization": "~14.1.1", "expo-notifications": "~0.18.1", "expo-print": "~12.2.1", "expo-secure-store": "~12.1.1", "expo-sharing": "~11.2.2", "expo-splash-screen": "~0.18.2", "expo-status-bar": "~1.4.4", "expo-updates": "~0.16.4", "graphql": "^15.6.1", "i18n": "^0.14.1", "lottie-react-native": "5.1.4", "moment": "^2.29.1", "patch-package": "^6.2.2", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.71.14", "react-native-bluetooth-classic": "1.60.0-rc.30", "react-native-countdown-component": "^2.7.1", "react-native-elements": "^3.4.2", "react-native-flash-message": "^0.1.21", "react-native-gesture-handler": "~2.9.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-material-textfield": "^0.16.1", "react-native-reanimated": "~2.14.4", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0", "react-native-swipe-gestures": "^1.0.5", "react-native-tab-view": "^2.16.0", "react-native-web": "~0.18.11", "react-native-webview": "11.26.0", "sentry-expo": "~6.2.0", "subscriptions-transport-ws": "^0.9.16", "validate.js": "^0.13.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/metro-config": "^0.3.8", "babel-preset-expo": "^9.3.0", "eslint": "^7.1.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.20.0", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "lint-staged": "^10.2.7", "prettier": "2.0.5", "prettier-config-standard": "^1.0.1", "react-native-dotenv": "^3.4.11"}, "private": true, "name": "Firespoon_Restaurant", "version": "1.0.0"}