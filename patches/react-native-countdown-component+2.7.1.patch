diff --git a/node_modules/react-native-countdown-component/index.js b/node_modules/react-native-countdown-component/index.js
index b546b82..f714717 100644
--- a/node_modules/react-native-countdown-component/index.js
+++ b/node_modules/react-native-countdown-component/index.js
@@ -56,7 +56,7 @@ class CountDown extends React.Component {
 
   componentWillUnmount() {
     clearInterval(this.timer);
-    AppState.removeEventListener('change', this._handleAppStateChange);
+    AppState.addEventListener('change', this._handleAppStateChange);
   }
 
   componentDidUpdate(prevProps, prevState) {
