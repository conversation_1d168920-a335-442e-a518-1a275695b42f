diff --git a/node_modules/react-native-material-textfield/src/components/affix/index.js b/node_modules/react-native-material-textfield/src/components/affix/index.js
index 0f85022..c12b3a6 100644
--- a/node_modules/react-native-material-textfield/src/components/affix/index.js
+++ b/node_modules/react-native-material-textfield/src/components/affix/index.js
@@ -11,7 +11,7 @@ export default class Affix extends PureComponent {
 
   static propTypes = {
     numberOfLines: PropTypes.number,
-    style: Animated.Text.propTypes.style,
+    style: PropTypes.object,
 
     color: PropTypes.string.isRequired,
     fontSize: PropTypes.number.isRequired,
diff --git a/node_modules/react-native-material-textfield/src/components/field/index.js b/node_modules/react-native-material-textfield/src/components/field/index.js
index 494bbaa..9bbf2e2 100644
--- a/node_modules/react-native-material-textfield/src/components/field/index.js
+++ b/node_modules/react-native-material-textfield/src/components/field/index.js
@@ -221,6 +221,7 @@ export default class TextField extends PureComponent {
 
     let options = {
       toValue: this.focusState(),
+      useNativeDriver: false,
       duration,
     };
 
diff --git a/node_modules/react-native-material-textfield/src/components/helper/index.js b/node_modules/react-native-material-textfield/src/components/helper/index.js
index 6060f9f..fe9d9c4 100644
--- a/node_modules/react-native-material-textfield/src/components/helper/index.js
+++ b/node_modules/react-native-material-textfield/src/components/helper/index.js
@@ -11,7 +11,7 @@ export default class Helper extends PureComponent {
 
     disabled: PropTypes.bool,
 
-    style: Animated.Text.propTypes.style,
+    style: PropTypes.object,
 
     baseColor: PropTypes.string,
     errorColor: PropTypes.string,
diff --git a/node_modules/react-native-material-textfield/src/components/label/index.js b/node_modules/react-native-material-textfield/src/components/label/index.js
index 82eaf03..809fcdd 100644
--- a/node_modules/react-native-material-textfield/src/components/label/index.js
+++ b/node_modules/react-native-material-textfield/src/components/label/index.js
@@ -43,7 +43,7 @@ export default class Label extends PureComponent {
       y1: PropTypes.number,
     }),
 
-    style: Animated.Text.propTypes.style,
+    style: PropTypes.object,
     label: PropTypes.string,
   };
 