# Node modules
node_modules/

# iOS
ios/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build directories
build/
dist/
.expo/
web-build/

# Expo files
.expo-shared/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDEs and editors
.idea/
.vscode/
*.sublime-workspace

# Mac system files
.DS_Store

# Temporary files
tmp/
temp/

# Package files
package-lock.json
yarn.lock

# Test coverage
coverage/

# EAS build
eas.json

# Android specific
android/app/build/
android/.gradle/
android/build/
android/local.properties
android/gradlew
android/gradlew.bat
android/gradle/
android/captures/
android/.project
android/.settings/
android/app/.project
android/app/.settings/
android/app/.classpath
android/*.iml
android/app/*.iml
android/.cxx/

# Google service accounts and configurations
google-services.json
google-service-account.json
**/google-services.json
**/google-service-account.json

# Other sensitive files
*.key
*.pem
*.p8
*.p12
*.keystore
*.jks
android/app/build.gradle
android/app/proguard-rules.pro
android/app/src/debug/AndroidManifest.xml
android/app/src/debug/java/com/firespoon/restaurant/ReactNativeFlipper.java
android/app/src/main/AndroidManifest.xml
android/app/src/main/java/com/firespoon/restaurant/MainActivity.java
android/app/src/main/java/com/firespoon/restaurant/MainApplication.java
android/app/src/main/res/drawable/rn_edit_text_material.xml
android/app/src/main/res/drawable/splashscreen.xml
android/app/src/main/res/drawable-hdpi/splashscreen_image.png
android/app/src/main/res/drawable-mdpi/splashscreen_image.png
android/app/src/main/res/drawable-xhdpi/splashscreen_image.png
android/app/src/main/res/drawable-xxhdpi/splashscreen_image.png
android/app/src/main/res/drawable-xxxhdpi/splashscreen_image.png
android/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml
android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml
android/app/src/main/res/mipmap-hdpi/ic_launcher_foreground.png
android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png
android/app/src/main/res/mipmap-hdpi/ic_launcher.png
android/app/src/main/res/mipmap-mdpi/ic_launcher_foreground.png
android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png
android/app/src/main/res/mipmap-mdpi/ic_launcher.png
android/app/src/main/res/mipmap-xhdpi/ic_launcher_foreground.png
android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png
android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
android/app/src/main/res/mipmap-xxhdpi/ic_launcher_foreground.png
android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png
android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_foreground.png
android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png
android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png
android/app/src/main/res/values/colors.xml
android/app/src/main/res/values/strings.xml
android/app/src/main/res/values/styles.xml
android/app/src/main/res/values-night/colors.xml
android/app/src/release/java/com/firespoon/restaurant/ReactNativeFlipper.java
android/.gitignore
