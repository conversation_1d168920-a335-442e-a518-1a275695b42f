{"expo": {"name": "Firerestaurant", "slug": "firerestaurant-pergqjay4tvyswxaxnzeu", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/icon.png", "splash": {"image": "./assets/splash.png", "resizeMode": "cover", "backgroundColor": "#ff325d"}, "assetBundlePatterns": ["**/*"], "plugins": [["expo-updates", {"username": "firespoon"}], "sentry-expo"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.firespoon.restaurant", "infoPlist": {"UIBackgroundModes": ["audio", "remote-notification", "audio", "remote-notification"]}}, "android": {"versionCode": 37, "package": "com.firespoon.restaurant", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "permissions": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_SCAN", "android.permission.BLUETOOTH_CONNECT", "android.permission.BLUETOOTH_ADVERTISE", "android.permission.ACCESS_FINE_LOCATION"]}, "hooks": {"postPublish": [{"file": "sentry-expo/upload-sourcemaps", "config": {"organization": "firespoon", "project": "firerestaurant"}}]}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "cc4d5b18-a0c2-4bd6-ab52-84ae650f4905"}}, "runtimeVersion": {"policy": "sdkVersion"}, "updates": {"url": "https://u.expo.dev/cc4d5b18-a0c2-4bd6-ab52-84ae650f4905"}, "owner": "bp1853", "sdkVersion": "48.0.0"}}