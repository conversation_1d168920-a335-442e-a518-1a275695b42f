/*****************************
 * environment.js
 * path: '/environment.js' (root of your project)
 ******************************/

import * as Updates from 'expo-updates'
import {
  GRAPHQL_URL_DEV,
  WS_GRAPHQL_URL_DEV,
  GRAPHQL_URL_STAGING,
  WS_GRAPHQL_URL_STAGING,
  GRAPHQL_URL_PRODUCTION,
  WS_GRAPHQL_URL_PRODUCTION,
  SENTRY_DSN_DEV,
  SENTRY_DSN_STAGING,
  SENTRY_DSN_PRODUCTION
} from '@env'

// Debug logs
console.log('Raw Environment Variables:', {
  GRAPHQL_URL_DEV,
  WS_GRAPHQL_URL_DEV,
  env: process.env.ENVIRONMENT,
  envFile: process.env.ENVFILE
})

const ENV = {
  development: {
    GRAPHQL_URL: GRAPHQL_URL_DEV,
    WS_GRAPHQL_URL: WS_GRAPHQL_URL_DEV,
    sentry: { dsn: SENTRY_DSN_DEV }
  },
  staging: {
    GRAPHQL_URL: GRAPHQL_URL_STAGING,
    WS_GRAPHQL_URL: WS_GRAPHQL_URL_STAGING,
    sentry: { dsn: SENTRY_DSN_STAGING }
  },
  production: {
    GRAPHQL_URL: GRAPHQL_URL_PRODUCTION,
    WS_GRAPHQL_URL: WS_GRAPHQL_URL_PRODUCTION,
    sentry: { dsn: SENTRY_DSN_PRODUCTION }
  }
}

const getEnvVars = (env = Updates.releaseChannel) => {
  // What is __DEV__ ?
  // This variable is set to true when react-native is running in Dev mode.
  // __DEV__ is true when run locally, but false when published.
  // eslint-disable-next-line no-undef
  if (env === 'production') {
    return ENV.production
  } else if (env === 'staging') {
    return ENV.staging
  } else {
    return ENV.development
  }
}

export default getEnvVars
