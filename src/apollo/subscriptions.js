export const subscribePlaceOrder = `subscription SubscribePlaceOrder($restaurant:String!){
    subscribePlaceOrder(restaurant:$restaurant){
        customerId
        origin
        order{
          _id
          orderId
          orderAmount
          restaurantId
          restaurantName
          orderStatus
          deliveryAddress
          deliveryInstructions
          items{
            _id
            title
            description
            image
            quantity
            variation{
              _id
              title
              price
              discounted
            }
            addons{
              _id
              options{
                _id
                title
                description
                price
              }
              description
              title
              quantityMinimum
              quantityMaximum
            }
            specialInstructions
            isActive
            createdAt
            updatedAt
          }
          paymentMethod
          paidAmount
          tipping
          taxationAmount
          status
          paymentStatus
          reason
          isActive
          createdAt
          orderDate
          deliveryCharges
          isPickedUp
          preparationTime
          acceptedAt
          isRinged
    }
  }
}`

export const subscribeOrderStatus = `subscription SubscribeOrderStatus($_id:String!){
    subscribeOrderStatus(_id:$_id){
        _id
        orderId
        orderStatus
        rider{
            name
        }
    }
}`

export const subscriptionOrder = `subscription SubscriptionOrder($id:String!){
  subscriptionOrder(id:$id){
    _id
    orderId
    orderAmount
    restaurantId
    restaurantName
    orderStatus
    deliveryAddress
    deliveryInstructions
    items{
      _id
      title
      description
      image
      quantity
      variation{
        _id
        title
        price
        discounted
      }
      addons{
        _id
        options{
          _id
          title
          description
          price
        }
        description
        title
        quantityMinimum
        quantityMaximum
      }
      specialInstructions
      isActive
      createdAt
      updatedAt
    }
    paymentMethod
    paidAmount
    tipping
    taxationAmount
    status
    paymentStatus
    reason
    isActive
    createdAt
    orderDate
    deliveryCharges
    isPickedUp
    preparationTime
    acceptedAt
    isRinged
  }
}`
