export const orders = `
  query Orders {
    restaurantOrders {
      _id
      orderId
      orderAmount
      restaurantId
      restaurantName
      orderStatus
      customerPhone
      deliveryAddress
      deliveryInstructions
      items {
        _id
        title
        description
        image
        quantity
        variation {
          _id
          title
          price
          discounted
        }
        addons {
          _id
          options {
            _id
            title
            description
            price
          }
          description
          title
          quantityMinimum
          quantityMaximum
        }
        specialInstructions
        isActive
        createdAt
        updatedAt
      }
      paymentMethod
      paidAmount
      tipping
      taxationAmount
      status
      paymentStatus
      reason
      isActive
      createdAt
      orderDate
      deliveryCharges
      isPickedUp
      preparationTime
      acceptedAt
      isRinged
      refunds {
        _id
        refundId
        orderId
        refundType
        requestAmount
        finalRefundAmount
        reason
        reasonText
        feeBearer
        transactionFee
        status
        stripeRefundId
        createdAt
        processedAt
        completedAt
        errorMessage
      }
      totalRefunded
      refundStatus
    }
  }
`

export const configuration = `
  query Configuration {
    configuration {
      _id
      currency
      currencySymbol
    }
  }
`

export const restaurantInfo = `
  query Restaurant($id: String) {
    restaurant(id: $id) {
      _id
      orderId
      orderPrefix
      name
      image
      address
      location {
        coordinates
      }
      deliveryTime
      username
      isAvailable
      notificationToken
      enableNotification
      openingTimes {
        day
        times {
          startTime
          endTime
        }
      }
    }
  }
`

export const getRefund = `
  query GetRefund($refundId: String!) {
    getRefund(refundId: $refundId) {
      _id
      refundId
      orderId
      refundType
      requestAmount
      finalRefundAmount
      reason
      reasonText
      feeBearer
      transactionFee
      status
      stripeRefundId
      createdAt
      processedAt
      completedAt
      errorMessage
    }
  }
`

export const getOrderRefunds = `
  query GetOrderRefunds($orderId: String!) {
    getOrderRefunds(orderId: $orderId) {
      _id
      refundId
      orderId
      refundType
      requestAmount
      finalRefundAmount
      reason
      reasonText
      feeBearer
      transactionFee
      status
      stripeRefundId
      createdAt
      processedAt
      completedAt
      errorMessage
    }
  }
`
