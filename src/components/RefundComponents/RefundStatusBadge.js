import React from 'react'
import { View, Text } from 'react-native'
import { Badge } from 'react-native-elements'
import { colors } from '../../utilities'
import { ORDER_REFUND_STATUS } from '../../utilities/orderEnums'

/**
 * 退款状态徽章组件
 * @param {Object} props
 * @param {string} props.refundStatus - 退款状态
 * @param {number} props.totalRefunded - 退款总金额
 * @param {Object} props.configuration - 配置对象（包含货币符号）
 * @param {string} props.size - 徽章大小 ('small', 'medium', 'large')
 */
export default function RefundStatusBadge({ 
  refundStatus, 
  totalRefunded, 
  configuration, 
  size = 'medium' 
}) {
  if (refundStatus === ORDER_REFUND_STATUS.NONE) {
    return null
  }

  const getBadgeStyle = () => {
    const baseStyle = {
      backgroundColor: colors.red,
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4
    }

    switch (size) {
      case 'small':
        return { ...baseStyle, paddingHorizontal: 6, paddingVertical: 2 }
      case 'large':
        return { ...baseStyle, paddingHorizontal: 12, paddingVertical: 6 }
      default:
        return baseStyle
    }
  }

  const getTextStyle = () => {
    const baseStyle = {
      color: 'white',
      fontWeight: 'bold',
      fontSize: 12
    }

    switch (size) {
      case 'small':
        return { ...baseStyle, fontSize: 10 }
      case 'large':
        return { ...baseStyle, fontSize: 14 }
      default:
        return baseStyle
    }
  }

  const getStatusText = () => {
    switch (refundStatus) {
      case ORDER_REFUND_STATUS.PARTIAL:
        return 'Partially Refunded'
      case ORDER_REFUND_STATUS.FULL:
        return 'Fully Refunded'
      default:
        return 'Refunded'
    }
  }

  return (
    <View style={{ flexDirection: 'row', alignItems: 'center', marginVertical: 2 }}>
      <View style={getBadgeStyle()}>
        <Text style={getTextStyle()}>
          {getStatusText()}
        </Text>
      </View>
      {totalRefunded > 0 && (
        <Text style={{
          marginLeft: 8,
          color: colors.red,
          fontWeight: '500',
          fontSize: size === 'small' ? 10 : size === 'large' ? 14 : 12
        }}>
          -{configuration.currencySymbol}{totalRefunded.toFixed(2)}
        </Text>
      )}
    </View>
  )
}
