import React, { useContext } from 'react'
import { View, Text, FlatList } from 'react-native'
import { TextDefault } from '..'
import { colors } from '../../utilities'
import { Configuration } from '../../ui/context'
import { getRefundStatusText, getRefundReasonText } from '../../utilities/orderEnums'
import moment from 'moment'

/**
 * 退款历史列表组件
 * @param {Object} props
 * @param {Array} props.refunds - 退款记录数组
 * @param {Object} props.style - 自定义样式
 */
export default function RefundHistoryList({ refunds = [], style = {} }) {
  const configuration = useContext(Configuration.Context)

  if (!refunds || refunds.length === 0) {
    return (
      <View style={[{ padding: 16, alignItems: 'center' }, style]}>
        <TextDefault H6 textColor={colors.fontSecondColor}>
          No refund history
        </TextDefault>
      </View>
    )
  }

  const renderRefundItem = ({ item: refund, index }) => (
    <View style={{
      backgroundColor: '#f8f9fa',
      borderRadius: 8,
      padding: 12,
      marginBottom: 8,
      borderLeftWidth: 4,
      borderLeftColor: getStatusColor(refund.status)
    }}>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 14,
            fontWeight: 'bold',
            color: colors.fontMainColor,
            marginBottom: 4
          }}>
            Refund #{refund.refundId || `${index + 1}`}
          </Text>
          
          <Text style={{
            fontSize: 12,
            color: colors.fontSecondColor,
            marginBottom: 2
          }}>
            {moment(refund.createdAt).format('MMM DD, YYYY HH:mm')}
          </Text>
          
          <Text style={{
            fontSize: 12,
            color: colors.fontSecondColor,
            marginBottom: 2
          }}>
            Reason: {getRefundReasonText(refund.reason)}
          </Text>
          
          {refund.reasonText && (
            <Text style={{
              fontSize: 12,
              color: colors.fontSecondColor,
              marginBottom: 2,
              fontStyle: 'italic'
            }}>
              "{refund.reasonText}"
            </Text>
          )}
          
          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 4 }}>
            <Text style={{
              fontSize: 12,
              color: getStatusColor(refund.status),
              fontWeight: '500'
            }}>
              {getRefundStatusText(refund.status)}
            </Text>
            
            {refund.status === 'FAILED' && refund.errorMessage && (
              <Text style={{
                fontSize: 10,
                color: colors.red,
                marginLeft: 8,
                flex: 1
              }}>
                Error: {refund.errorMessage}
              </Text>
            )}
          </View>
        </View>
        
        <View style={{ alignItems: 'flex-end' }}>
          <Text style={{
            fontSize: 16,
            fontWeight: 'bold',
            color: colors.red
          }}>
            -{configuration.currencySymbol}{refund.finalRefundAmount.toFixed(2)}
          </Text>
          
          {refund.requestAmount !== refund.finalRefundAmount && (
            <Text style={{
              fontSize: 12,
              color: colors.fontSecondColor,
              textDecorationLine: 'line-through'
            }}>
              {configuration.currencySymbol}{refund.requestAmount.toFixed(2)}
            </Text>
          )}
          
          {refund.transactionFee > 0 && (
            <Text style={{
              fontSize: 10,
              color: colors.fontSecondColor,
              marginTop: 2
            }}>
              Fee: {configuration.currencySymbol}{refund.transactionFee.toFixed(2)}
            </Text>
          )}
        </View>
      </View>
    </View>
  )

  const getStatusColor = (status) => {
    switch (status) {
      case 'SUCCEEDED':
        return colors.green
      case 'FAILED':
        return colors.red
      case 'PROCESSING':
        return colors.blue
      case 'PENDING':
        return colors.orange || '#FFA500'
      default:
        return colors.fontSecondColor
    }
  }

  return (
    <View style={style}>
      <TextDefault H5 bold style={{ marginBottom: 12 }}>
        Refund History ({refunds.length})
      </TextDefault>
      
      <FlatList
        data={refunds}
        renderItem={renderRefundItem}
        keyExtractor={(item, index) => item._id || `refund-${index}`}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false} // 禁用滚动，让父组件处理
      />
    </View>
  )
}
