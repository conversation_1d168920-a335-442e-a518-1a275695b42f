// ============================================================================
// Order Status Enum
// ============================================================================

export const ORDER_STATUS = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  PICKED: 'PICKED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  COMPLETED: 'COMPLETED',
  ASSIGNED: 'ASSIGNED',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
  REFUNDED: 'REFUNDED'
}

// ============================================================================
// Refund Related Enums
// ============================================================================

export const REFUND_REASON = {
  MERCHANT_OUT_OF_STOCK: 'MERCHANT_OUT_OF_STOCK',
  MERCHANT_CANNOT_DELIVER: 'MERCHANT_CANNOT_DELIVER',
  MERCHANT_OTHER: 'MERCHANT_OTHER',
  CUSTOMER_CANCELLED: 'CUSTOMER_CANCELLED'
}

export const REFUND_TYPE = {
  FULL: 'FULL',
  PARTIAL: 'PARTIAL'
}

export const REFUND_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SUCCEEDED: 'SUCCEEDED',
  FAILED: 'FAILED'
}

export const ORDER_REFUND_STATUS = {
  NONE: 'NONE',
  PARTIAL: 'PARTIAL',
  FULL: 'FULL'
}

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * 获取订单状态的显示文本
 * @param {string} status - 订单状态
 * @returns {string} 显示文本
 */
export const getOrderStatusText = (status) => {
  const statusTexts = {
    [ORDER_STATUS.PENDING]: 'Pending',
    [ORDER_STATUS.ACCEPTED]: 'Accepted',
    [ORDER_STATUS.PICKED]: 'Picked',
    [ORDER_STATUS.DELIVERED]: 'Delivered',
    [ORDER_STATUS.CANCELLED]: 'Cancelled',
    [ORDER_STATUS.COMPLETED]: 'Completed',
    [ORDER_STATUS.ASSIGNED]: 'Assigned',
    [ORDER_STATUS.PARTIALLY_REFUNDED]: 'Partially Refunded',
    [ORDER_STATUS.REFUNDED]: 'Refunded'
  }
  return statusTexts[status] || status
}

/**
 * 获取退款原因的显示文本
 * @param {string} reason - 退款原因
 * @returns {string} 显示文本
 */
export const getRefundReasonText = (reason) => {
  const reasonTexts = {
    [REFUND_REASON.MERCHANT_OUT_OF_STOCK]: 'Out of Stock',
    [REFUND_REASON.MERCHANT_CANNOT_DELIVER]: 'Cannot Deliver',
    [REFUND_REASON.MERCHANT_OTHER]: 'Other Merchant Issue',
    [REFUND_REASON.CUSTOMER_CANCELLED]: 'Customer Cancelled'
  }
  return reasonTexts[reason] || reason
}

/**
 * 获取退款状态的显示文本
 * @param {string} status - 退款状态
 * @returns {string} 显示文本
 */
export const getRefundStatusText = (status) => {
  const statusTexts = {
    [REFUND_STATUS.PENDING]: 'Pending',
    [REFUND_STATUS.PROCESSING]: 'Processing',
    [REFUND_STATUS.SUCCEEDED]: 'Succeeded',
    [REFUND_STATUS.FAILED]: 'Failed'
  }
  return statusTexts[status] || status
}

/**
 * 检查订单是否可以退款
 * @param {string} orderStatus - 订单状态
 * @param {string} refundStatus - 退款状态
 * @returns {boolean} 是否可以退款
 */
export const canRefundOrder = (orderStatus, refundStatus) => {
  // 只有已完成、已交付或已取消的订单可以退款
  const refundableStatuses = [
    ORDER_STATUS.COMPLETED,
    ORDER_STATUS.DELIVERED,
    ORDER_STATUS.CANCELLED
  ]
  
  // 已经全额退款的订单不能再退款
  if (refundStatus === ORDER_REFUND_STATUS.FULL) {
    return false
  }
  
  return refundableStatuses.includes(orderStatus)
}

/**
 * 检查订单是否可以部分退款
 * @param {string} orderStatus - 订单状态
 * @param {string} refundStatus - 退款状态
 * @returns {boolean} 是否可以部分退款
 */
export const canPartialRefund = (orderStatus, refundStatus) => {
  return canRefundOrder(orderStatus, refundStatus) && 
         refundStatus !== ORDER_REFUND_STATUS.PARTIAL
}

/**
 * 获取处理中的订单状态列表
 * @returns {Array<string>} 处理中的订单状态
 */
export const getProcessingOrderStatuses = () => {
  return [
    ORDER_STATUS.ACCEPTED,
    ORDER_STATUS.ASSIGNED,
    ORDER_STATUS.PICKED
  ]
}

/**
 * 获取已完成的订单状态列表
 * @returns {Array<string>} 已完成的订单状态
 */
export const getCompletedOrderStatuses = () => {
  return [
    ORDER_STATUS.DELIVERED,
    ORDER_STATUS.COMPLETED
  ]
}

/**
 * 获取退款相关的订单状态列表
 * @returns {Array<string>} 退款相关的订单状态
 */
export const getRefundedOrderStatuses = () => {
  return [
    ORDER_STATUS.PARTIALLY_REFUNDED,
    ORDER_STATUS.REFUNDED
  ]
}
