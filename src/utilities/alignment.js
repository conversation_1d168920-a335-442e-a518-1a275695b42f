import { scale } from './scaling'

const XSMALL = 5
const SMALL = 10
const MEDIUM = 15
const LARGE = 20
export const alignment = {
  MxSmall: {
    margin: scale(XSMALL)
  },
  MBxSmall: {
    marginBottom: scale(XSMALL)
  },
  MTxSmall: {
    marginTop: scale(XSMALL)
  },
  MRxSmall: {
    marginRight: scale(XSMALL)
  },
  MLxSmall: {
    marginLeft: scale(XSMALL)
  },

  Msmall: {
    margin: scale(SMALL)
  },
  MBsmall: {
    marginBottom: scale(SMALL)
  },
  MTsmall: {
    marginTop: scale(SMALL)
  },
  MRsmall: {
    marginRight: scale(SMALL)
  },
  MLsmall: {
    marginLeft: scale(SMALL)
  },

  Mmedium: {
    margin: scale(MEDIUM)
  },
  MBmedium: {
    marginBottom: scale(MEDIUM)
  },
  MTmedium: {
    marginTop: scale(MEDIUM)
  },
  MRmedium: {
    marginRight: scale(MEDIUM)
  },
  MLmedium: {
    marginLeft: scale(MEDIUM)
  },
  Mlarge: {
    margin: scale(LARGE)
  },
  MBlarge: {
    marginBottom: scale(LARGE)
  },
  MTlarge: {
    marginTop: scale(LARGE)
  },
  MRlarge: {
    marginRight: scale(LARGE)
  },
  MLlarge: {
    marginLeft: scale(LARGE)
  },

  // Padding
  PxSmall: {
    padding: scale(XSMALL)
  },
  PBxSmall: {
    paddingBottom: scale(XSMALL)
  },
  PTxSmall: {
    paddingTop: scale(XSMALL)
  },
  PRxSmall: {
    paddingRight: scale(XSMALL)
  },
  PLxSmall: {
    paddingLeft: scale(XSMALL)
  },

  Psmall: {
    padding: scale(SMALL)
  },
  PBsmall: {
    paddingBottom: scale(SMALL)
  },
  PTsmall: {
    paddingTop: scale(SMALL)
  },
  PRsmall: {
    paddingRight: scale(SMALL)
  },
  PLsmall: {
    paddingLeft: scale(SMALL)
  },

  Pmedium: {
    padding: scale(MEDIUM)
  },
  PBmedium: {
    paddingBottom: scale(MEDIUM)
  },
  PTmedium: {
    paddingTop: scale(MEDIUM)
  },
  PRmedium: {
    paddingRight: scale(MEDIUM)
  },
  PLmedium: {
    paddingLeft: scale(MEDIUM)
  },

  Plarge: {
    padding: scale(LARGE)
  },
  PBlarge: {
    paddingBottom: scale(LARGE)
  },
  PTlarge: {
    paddingTop: scale(LARGE)
  },
  PRlarge: {
    paddingRight: scale(LARGE)
  },
  PLlarge: {
    paddingLeft: scale(LARGE)
  }
}
