/**
 * Format a receipt as plain text for the InnerPrinter
 * This function converts an order object into a plain text receipt
 * suitable for printing on the Sunmi InnerPrinter
 * Width: 48 characters (384 dots for 58mm paper)
 */
export const formatTextReceipt = order => {
  console.log('Formatting receipt for order:', order ? order._id : 'undefined')

  if (!order) {
    console.log('Order is undefined or null')
    return ''
  }

  try {
    // Check if order has required properties
    if (!order.items || !Array.isArray(order.items)) {
      console.log('Order items is undefined or not an array:', order.items)
      return 'ERROR: Invalid order structure (missing items array)'
    }

    const address =
      order.shippingMethod === 'PICKUP'
        ? 'PICKUP'
        : `${order.deliveryAddress || 'N/A'}`
    const {
      customerPhone,
      taxationAmount: tax = 0,
      tipping: tip = 0,
      paidAmount = 0,
      orderAmount = 0
    } = order
    const deliveryCharges = order.deliveryCharges || 0
    const currencySymbol = order.currencySymbol || '$'

    // Format header with 48 char width
    let receipt = ''
    receipt += `${order.restaurantName}\n`
    receipt += '================================================\n\n'

    // Format contact info
    receipt += `Del to: ${address}\n`
    receipt += `Phone: ${customerPhone}\n\n`

    // Format items table header (48 chars total)
    receipt += 'Item                               Qty   Price\n'
    receipt += '----------------------------------------------\n'

    // Format items
    order.items.forEach(item => {
      if (!item) {
        console.log('Item is undefined or null')
        return
      }

      try {
        const variation = item.variation || { price: 0, title: '' }
        const addons = item.addons || []

        const price = (
          variation.price +
          addons
            .map(addon =>
              (addon.options || []).reduce(
                (prev, curr) => prev + (curr.price || 0),
                0
              )
            )
            .reduce((prev, curr) => prev + curr, 0)
        ).toFixed(2)

        // Format item name (truncate if too long)
        let itemName = item.title
        if (variation.title) {
          itemName += `:${variation.title}`
        }

        // Truncate name if too long (33 chars - increased from 30)
        if (itemName.length > 33) {
          itemName = itemName.substring(0, 30) + '...'
        }

        // Pad strings to align columns (48 chars total)
        const paddedName = itemName.padEnd(33) // 增加到33字符
        const paddedQty = (item.quantity || 1).toString().padStart(3) // 减少到3字符
        const paddedPrice = `${currencySymbol}${price}`.padStart(10)

        receipt += `${paddedName}${paddedQty} ${paddedPrice}\n`

        // Add addon options if any (indented)
        addons.forEach(addon => {
          if (!addon) return
          ;(addon.options || []).forEach(option => {
            if (!option) return
            receipt += `  ${(option.title || 'Option').padEnd(46)}\n`
          })
        })
      } catch (itemError) {
        console.log('Error processing item:', itemError)
        receipt += 'Error processing item\n'
      }
    })

    receipt += '------------------------------------------------\n'

    // Format totals (aligned right, 50 chars total)
    const taxStr = `${currencySymbol}${tax.toFixed(2)}`.padStart(10)
    const tipStr = `${currencySymbol}${tip.toFixed(2)}`.padStart(10)
    const deliveryStr = `${currencySymbol}${deliveryCharges.toFixed(
      2
    )}`.padStart(10)
    const totalStr = `${currencySymbol}${orderAmount.toFixed(2)}`.padStart(10)
    const paidStr = `${currencySymbol}${paidAmount.toFixed(2)}`.padStart(10)

    receipt += `Tax:                                ${taxStr}\n`
    receipt += `Tip:                                ${tipStr}\n`
    receipt += `Delivery ch.:                       ${deliveryStr}\n`
    receipt += `Total:                              ${totalStr}\n`
    receipt += `Paid:                               ${paidStr}\n\n`

    // Footer
    receipt += '================================================\n'
    receipt += '          Thank you for your business!\n\n'

    return receipt
  } catch (error) {
    console.log('Error formatting receipt:', error)
    return 'ERROR: Failed to format receipt'
  }
}
