/**
 * BluetoothConnectionManager class for managing persistent connections to Bluetooth devices
 * This singleton class maintains a connection to the InnerPrinter across multiple print jobs
 */
import BluetoothUtil from './BluetoothUtil'

class BluetoothConnectionManager {
  constructor() {
    this.connection = null
    this.device = null
    this.isConnecting = false
    this.connectionPromise = null
    this.connectionAttempts = 0
    this.maxConnectionAttempts = 3
    this.lastConnectionTime = 0
    this.connectionTimeout = 30000 // 30 seconds
  }

  /**
   * Get the singleton instance of the BluetoothConnectionManager
   * @returns {BluetoothConnectionManager} The singleton instance
   */
  static getInstance() {
    if (!BluetoothConnectionManager.instance) {
      BluetoothConnectionManager.instance = new BluetoothConnectionManager()
    }
    return BluetoothConnectionManager.instance
  }

  /**
   * Reset connection state
   * @private
   */
  _resetConnectionState() {
    this.connection = null
    this.device = null
    this.isConnecting = false
    this.connectionPromise = null
    this.connectionAttempts = 0
    this.lastConnectionTime = 0
  }

  /**
   * Establish a connection to the InnerPrinter
   * @private
   * @returns {Promise<Connection|null>} The connection or null if failed
   */
  async _establishConnection() {
    console.log(
      '_establishConnection called, attempt:',
      this.connectionAttempts + 1
    )

    try {
      // Check if we've exceeded the maximum number of connection attempts
      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        console.log('Maximum connection attempts reached')
        throw new Error('Maximum connection attempts reached')
      }

      this.connectionAttempts++

      // Request Bluetooth permissions
      const permissionsGranted = await BluetoothUtil.requestBluetoothPermissions()
      if (!permissionsGranted) {
        console.log('Bluetooth permissions not granted')
        throw new Error('Bluetooth permissions not granted')
      }

      // Check if Bluetooth is available
      const isAvailable = await BluetoothUtil.isBluetoothAvailable()
      if (!isAvailable) {
        console.log('Bluetooth is not available, attempting to enable')
        // Try to enable Bluetooth
        const enabled = await BluetoothUtil.enableBluetooth()
        if (!enabled) {
          console.log('Failed to enable Bluetooth')
          throw new Error('Failed to enable Bluetooth')
        }
      }

      // Find the InnerPrinter device
      const device = await BluetoothUtil.findInnerPrinter()
      if (!device) {
        console.log('InnerPrinter device not found')
        throw new Error('InnerPrinter device not found')
      }

      this.device = device
      console.log('Found InnerPrinter device:', device.name, device.address)

      // Connect to the device
      const connection = await BluetoothUtil.connectToDevice(device)
      if (!connection) {
        console.log('Failed to connect to InnerPrinter')
        throw new Error('Failed to connect to InnerPrinter')
      }

      console.log('Connected to InnerPrinter successfully')
      this.connection = connection
      this.lastConnectionTime = Date.now()
      this.connectionAttempts = 0

      return connection
    } catch (error) {
      console.error('Error in _establishConnection:', error)

      // If we haven't exceeded the maximum number of connection attempts, try again
      if (this.connectionAttempts < this.maxConnectionAttempts) {
        console.log(
          `Connection attempt ${this.connectionAttempts} failed, retrying...`
        )
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000))
        return this._establishConnection()
      }

      return null
    }
  }

  /**
   * Get a connection to the InnerPrinter
   * If a connection already exists, it will be reused
   * If not, a new connection will be established
   * @param {boolean} forceReconnect - Force a new connection even if one exists
   * @returns {Promise<Connection|null>} The connection or null if failed
   */
  async getConnection(forceReconnect = false) {
    console.log('getConnection called, forceReconnect:', forceReconnect)
    console.log('Current connection:', this.connection ? 'exists' : 'null')

    // If force reconnect is true, clear existing connection
    if (forceReconnect && this.connection) {
      console.log(
        'Force reconnect requested, disconnecting existing connection'
      )
      await this.disconnect()
    }

    // If we already have a connection, check if it's still valid and not timed out
    if (this.connection) {
      try {
        // Check if the connection has timed out
        const currentTime = Date.now()
        const connectionAge = currentTime - this.lastConnectionTime

        if (connectionAge > this.connectionTimeout) {
          console.log('Connection has timed out, reconnecting...')
          await this.disconnect()
        } else {
          // Check if the connection is still valid
          const isConnected = await this.connection.isConnected()
          console.log('Existing connection is still valid:', isConnected)

          if (isConnected) {
            // Update the last connection time
            this.lastConnectionTime = currentTime
            return this.connection
          } else {
            console.log('Connection is no longer valid, reconnecting...')
            this.connection = null
          }
        }
      } catch (error) {
        console.error('Error checking connection status:', error)
        this.connection = null
      }
    }

    // If we're already in the process of connecting, return the promise
    if (this.isConnecting && this.connectionPromise) {
      console.log('Already connecting, returning existing promise')
      return this.connectionPromise
    }

    // Start a new connection process
    this.isConnecting = true

    // Create a promise for the connection process
    this.connectionPromise = this._establishConnection().finally(() => {
      this.isConnecting = false
      if (!this.connection) {
        console.log(
          'Connection process completed but no connection was established'
        )
      }
    })

    return this.connectionPromise
  }

  /**
   * Disconnect from the InnerPrinter
   * @returns {Promise<boolean>} Whether disconnection was successful
   */
  async disconnect() {
    console.log('disconnect called')

    if (!this.connection) {
      console.log('No connection to disconnect')
      this._resetConnectionState()
      return true
    }

    try {
      await BluetoothUtil.disconnectFromDevice(this.connection)
      this._resetConnectionState()
      console.log('Disconnected from InnerPrinter')
      return true
    } catch (error) {
      console.error('Error disconnecting from InnerPrinter:', error)
      // Even if there's an error, reset the connection state
      this._resetConnectionState()
      return false
    }
  }
}

// Create the singleton instance
BluetoothConnectionManager.instance = null

export default BluetoothConnectionManager
