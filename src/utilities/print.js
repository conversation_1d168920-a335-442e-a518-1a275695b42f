import * as Print from 'expo-print'
import { Platform, Alert } from 'react-native'
import BluetoothUtil, { PRINTER_COMMANDS } from './BluetoothUtil'
import { formatTextReceipt } from './formatTextReceipt'
import BluetoothConnectionManager from './BluetoothConnectionManager'
import { Buffer } from 'buffer'

export const printAsync = async (order, printerUrl) => {
  try {
    const html = `
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        </head>
        <body style="text-align: center;">
          <h1 style="font-size: 50px; font-family: Helvetica Neue; font-weight: normal;">
            Order Receipt
          </h1>
          <div style="margin: 20px; border: 1px solid black; padding: 10px; text-align: left;">
            <h2>Order #${order._id ? order._id.substring(0, 8) : 'N/A'}</h2>
            <p>Date: ${
              order.createdAt
                ? new Date(order.createdAt).toLocaleString()
                : new Date().toLocaleString()
            }</p>
            <p>Table: ${order.table || 'N/A'}</p>
            <p>Customer: ${order.customer || 'N/A'}</p>
            <hr />
            <h3>Items:</h3>
            <ul>
              ${(order.items || [])
                .map(
                  item =>
                    `<li>${item.quantity || 1} x ${
                      item.name || 'Unknown Item'
                    } - ${order.currencySymbol || '$'}${
                      item.price !== undefined && item.price !== null
                        ? item.price.toFixed(2)
                        : '0.00'
                    }</li>`
                )
                .join('')}
            </ul>
            <hr />
            <h3>Total: ${order.currencySymbol || '$'}${
      order.total !== undefined && order.total !== null
        ? order.total.toFixed(2)
        : '0.00'
    }</h3>
          </div>
        </body>
      </html>
    `

    const { uri } = await Print.printToFileAsync({ html })

    if (Platform.OS === 'ios') {
      await Print.printAsync({
        uri,
        printerUrl
      })
    } else {
      await Print.printAsync({
        uri
      })
    }

    return true
  } catch (error) {
    console.log('print error', error)
    return false
  }
}

export const selectPrinterAsync = async () => {
  try {
    const printer = await Print.selectPrinterAsync()
    return printer
  } catch (error) {
    console.log('select printer error', error)
    return null
  }
}

/**
 * Print to InnerPrinter using Bluetooth
 * @param {Object} order The order to print
 * @returns {Promise<boolean>} Whether printing was successful
 */
export const printToInnerPrinter = async order => {
  console.log('printToInnerPrinter called')

  // Check platform
  if (Platform.OS !== 'android') {
    console.log('Not on Android platform')
    throw new Error('InnerPrinter is only available on Android')
  }

  try {
    // Get a connection from the BluetoothConnectionManager
    // This will reuse an existing connection if available, or create a new one if needed
    const connectionManager = BluetoothConnectionManager.getInstance()
    const connection = await connectionManager.getConnection()

    if (!connection) {
      console.log('Failed to get connection to InnerPrinter')
      Alert.alert(
        'Print Error',
        'Failed to connect to InnerPrinter. Please make sure the printer is connected and try again.'
      )
      return false
    }

    console.log('Connected to InnerPrinter')

    // Format receipt
    console.log('Formatting receipt...')
    const receiptText = formatTextReceipt(order)

    console.log('Converting receipt to bytes...', receiptText)

    // 初始化命令 - 重置打印机
    const initCommand = new Uint8Array(PRINTER_COMMANDS.INIT)
    await BluetoothUtil.sendData(initCommand, connection)

    // 设置多字节编码类型 - 0x1C 0x26
    const multiByteCommand = new Uint8Array([0x1c, 0x26])
    await BluetoothUtil.sendData(multiByteCommand, connection)

    // 设置为UTF-8编码 - 0x1C 0x43 0xFF
    const utf8Command = new Uint8Array([0x1c, 0x43, 0xff])
    await BluetoothUtil.sendData(utf8Command, connection)
    /*
    // 设置对齐方式 - 居中
    const alignCenterCommand = new Uint8Array(BluetoothUtil.COMMANDS.ALIGN_CENTER)
    await BluetoothUtil.sendData(alignCenterCommand, connection)

    // 设置文本大小 - 正常
    const textSizeCommand = new Uint8Array(BluetoothUtil.COMMANDS.TEXT_SIZE_NORMAL)
    await BluetoothUtil.sendData(textSizeCommand, connection)
    */
    // 将文本分成多个部分发送，避免一次发送过多数据
    const chunkSize = 100 // 每次发送100个字符
    for (let i = 0; i < receiptText.length; i += chunkSize) {
      const chunk = receiptText.substring(
        i,
        Math.min(i + chunkSize, receiptText.length)
      )
      const dataBytes = Buffer.from(chunk, 'utf-8')
      console.log(
        `Sending chunk ${i / chunkSize + 1}, length: ${dataBytes.length}`
      )

      // 发送数据
      const chunkResult = await BluetoothUtil.sendData(dataBytes, connection)
      if (!chunkResult) {
        console.log(`Failed to send chunk ${i / chunkSize + 1}`)
        return false
      }

      // 添加短暂延迟，确保打印机有足够时间处理
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    console.log('All data sent to InnerPrinter')
    /*
    // 设置对齐方式 - 左对齐
    const alignLeftCommand = new Uint8Array(BluetoothUtil.COMMANDS.ALIGN_LEFT);
    await BluetoothUtil.sendData(alignLeftCommand, connection);

    // 发送换行
    const lineFeedCommand = new Uint8Array(BluetoothUtil.COMMANDS.LINE_FEED);
    await BluetoothUtil.sendData(lineFeedCommand, connection);
    await BluetoothUtil.sendData(lineFeedCommand, connection);

    // 发送切纸命令
    console.log('Sending cut command...')
    const cutCommand = new Uint8Array(BluetoothUtil.COMMANDS.CUT_FULL)
    await BluetoothUtil.sendData(cutCommand, connection)
    */

    // Note: We no longer disconnect from the device here
    // The connection is maintained for future print jobs

    console.log('Print completed successfully')
    return true
  } catch (error) {
    console.error('Error printing to InnerPrinter:', error)
    return false
  }
}
