/**
 * BluetoothUtil class for connecting to Sunmi's InnerPrinter
 * This class provides utility methods to connect to the virtual Bluetooth printer
 * and send data to it for printing
 */
import { Platform, PermissionsAndroid } from 'react-native'
import RNBluetoothClassic from 'react-native-bluetooth-classic'

// Sunmi InnerPrinter constants
const INNERPRINTER_ADDRESS = '00:11:22:33:44:55' // This may need to be updated based on your device

// 打印机命令常量
export const PRINTER_COMMANDS = {
  INIT: [0x1b, 0x40], // 初始化打印机
  CUT_FULL: [0x1d, 0x56, 0x00], // 全切纸
  CUT_PARTIAL: [0x1d, 0x56, 0x01], // 半切纸
  LINE_FEED: [0x0a], // 换行
  BOLD_ON: [0x1b, 0x45, 0x01], // 加粗开
  BOLD_OFF: [0x1b, 0x45, 0x00], // 加粗关
  ALIGN_LEFT: [0x1b, 0x61, 0x00], // 左对齐
  ALIGN_CENTER: [0x1b, 0x61, 0x01], // 居中对齐
  ALIGN_RIGHT: [0x1b, 0x61, 0x02], // 右对齐
  CHINESE_MODE_ON: [0x1c, 0x26], // 中文模式开
  CHINESE_MODE_OFF: [0x1c, 0x2e], // 中文模式关
  TEXT_SIZE_NORMAL: [0x1d, 0x21, 0x00], // 正常字体大小
  TEXT_SIZE_DOUBLE: [0x1d, 0x21, 0x11] // 双倍字体大小
}

export default class BluetoothUtil {
  /**
   * Check if Bluetooth is available and enabled
   * @returns {Promise<boolean>} Whether Bluetooth is available and enabled
   */
  static async isBluetoothAvailable() {
    console.log('isBluetoothAvailable called')

    if (Platform.OS !== 'android') {
      console.log('Not on Android platform')
      return false
    }

    try {
      // Check if Bluetooth is enabled
      const enabled = await RNBluetoothClassic.isBluetoothEnabled()
      console.log('Bluetooth enabled:', enabled)
      return enabled
    } catch (error) {
      console.error('Error checking Bluetooth availability:', error)
      return false
    }
  }

  /**
   * Request Bluetooth permissions
   * @returns {Promise<boolean>} Whether permissions were granted
   */
  static async requestBluetoothPermissions() {
    console.log('requestBluetoothPermissions called')

    if (Platform.OS !== 'android') {
      return true
    }

    try {
      // Define permissions explicitly to avoid null permission issues
      const permissions = []

      // Add standard Bluetooth permissions that are available on all Android versions
      if (PermissionsAndroid.PERMISSIONS.BLUETOOTH) {
        permissions.push(PermissionsAndroid.PERMISSIONS.BLUETOOTH)
      } else {
        permissions.push('android.permission.BLUETOOTH')
      }

      if (PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN) {
        permissions.push(PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN)
      } else {
        permissions.push('android.permission.BLUETOOTH_ADMIN')
      }

      // Location permission is required for Bluetooth scanning on Android 6.0+
      if (PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION) {
        permissions.push(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
      } else {
        permissions.push('android.permission.ACCESS_FINE_LOCATION')
      }

      // For Android 12+ (API level 31+), we need BLUETOOTH_SCAN and BLUETOOTH_CONNECT
      if (parseInt(Platform.Version, 10) >= 31) {
        if (PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN) {
          permissions.push(PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN)
        } else {
          permissions.push('android.permission.BLUETOOTH_SCAN')
        }

        if (PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT) {
          permissions.push(PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT)
        } else {
          permissions.push('android.permission.BLUETOOTH_CONNECT')
        }
      }

      console.log('Requesting permissions:', permissions)

      // Request each permission individually to avoid issues with null permissions
      const results = {}
      for (const permission of permissions) {
        console.log('Requesting permission:', permission)
        try {
          const result = await PermissionsAndroid.request(permission, {
            title: 'Bluetooth Permission',
            message:
              'The app needs Bluetooth permission to connect to the printer',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK'
          })
          results[permission] = result
          console.log(`Permission ${permission} result:`, result)
        } catch (error) {
          console.error(`Error requesting permission ${permission}:`, error)
          results[permission] = 'error'
        }
      }

      // Check if all permissions were granted
      const allGranted = Object.values(results).every(
        result => result === PermissionsAndroid.RESULTS.GRANTED
      )

      console.log('All permissions granted:', allGranted)
      return allGranted
    } catch (error) {
      console.error('Error requesting Bluetooth permissions:', error)
      return false
    }
  }

  /**
   * Enable Bluetooth if it's not already enabled
   * @returns {Promise<boolean>} Whether Bluetooth was enabled successfully
   */
  static async enableBluetooth() {
    console.log('enableBluetooth called')

    if (Platform.OS !== 'android') {
      return false
    }

    try {
      // Check if Bluetooth is already enabled
      const isEnabled = await RNBluetoothClassic.isBluetoothEnabled()

      if (isEnabled) {
        console.log('Bluetooth is already enabled')
        return true
      }

      // Request user to enable Bluetooth
      // Note: This will show a system dialog to the user
      const enabled = await RNBluetoothClassic.requestBluetoothEnabled()
      console.log('Bluetooth enabled:', enabled)
      return enabled
    } catch (error) {
      console.error('Error enabling Bluetooth:', error)
      return false
    }
  }

  /**
   * Find the InnerPrinter device
   * @returns {Promise<Device|null>} The InnerPrinter device or null if not found
   */
  static async findInnerPrinter() {
    console.log('findInnerPrinter called')

    if (Platform.OS !== 'android') {
      return null
    }

    try {
      // Get list of bonded (paired) devices
      const devices = await RNBluetoothClassic.getBondedDevices()
      console.log('Bonded devices:', devices.length)

      // Find the InnerPrinter device
      // Note: You may need to adjust this logic based on how your printer identifies itself
      const printer = devices.find(
        device =>
          device.name?.includes('InnerPrinter') ||
          device.name?.includes('Printer') ||
          device.address === INNERPRINTER_ADDRESS
      )

      if (printer) {
        console.log('InnerPrinter found:', printer.name, printer.address)
        return printer
      } else {
        console.log('InnerPrinter not found in bonded devices')

        // If not found in bonded devices, try to discover devices
        console.log('Starting discovery...')
        await RNBluetoothClassic.startDiscovery()

        // Wait for discovery to find devices (adjust timeout as needed)
        await new Promise(resolve => setTimeout(resolve, 10000))

        // Stop discovery
        await RNBluetoothClassic.cancelDiscovery()

        // Check if the printer was found
        const discoveredDevices = await RNBluetoothClassic.getDiscoveredDevices()
        console.log('Discovered devices:', discoveredDevices.length)

        const discoveredPrinter = discoveredDevices.find(
          device =>
            device.name?.includes('InnerPrinter') ||
            device.name?.includes('Printer') ||
            device.address === INNERPRINTER_ADDRESS
        )

        if (discoveredPrinter) {
          console.log(
            'InnerPrinter found in discovered devices:',
            discoveredPrinter.name,
            discoveredPrinter.address
          )
          return discoveredPrinter
        }

        console.log('InnerPrinter not found')
        return null
      }
    } catch (error) {
      console.error('Error finding InnerPrinter:', error)
      return null
    }
  }

  /**
   * Connect to a Bluetooth device
   * @param {Device} device The device to connect to
   * @returns {Promise<Connection|null>} The connection or null if failed
   */
  static async connectToDevice(device) {
    console.log('connectToDevice called for device:', device.name)

    if (Platform.OS !== 'android') {
      return null
    }

    try {
      // Connect to the device
      const connection = await RNBluetoothClassic.connectToDevice(
        device.address
      )

      if (connection) {
        console.log('Connected to device:', device.name)
        return connection
      } else {
        console.log('Failed to connect to device:', device.name)
        return null
      }
    } catch (error) {
      console.error('Error connecting to device:', error)
      return null
    }
  }

  /**
   * Send data to a connected device
   * @param {Uint8Array} data The data to send
   * @param {Connection} connection The connection to send data through
   * @returns {Promise<boolean>} Whether data was sent successfully
   */
  static async sendData(data, connection) {
    console.log('sendData called:', data.toString())

    if (Platform.OS !== 'android') {
      return false
    }

    try {
      // Write data to the device
      const result = await connection.write(data)

      console.log('Data sent successfully', result)
      return true
    } catch (error) {
      console.error('Error sending data:', error)
      return false
    }
  }

  /**
   * Disconnect from a device
   * @param {Connection} connection The connection to close
   * @returns {Promise<boolean>} Whether disconnection was successful
   */
  static async disconnectFromDevice(connection) {
    console.log('disconnectFromDevice called')

    if (Platform.OS !== 'android') {
      return false
    }

    try {
      await connection.disconnect()
      console.log('Disconnected from device')
      return true
    } catch (error) {
      console.error('Error disconnecting from device:', error)
      return false
    }
  }
}
