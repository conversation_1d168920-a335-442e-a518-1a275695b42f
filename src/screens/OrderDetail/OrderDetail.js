import React, { useState } from 'react'
import { View, ActivityIndicator, ImageBackground, Modal, Text, TextInput, Alert } from 'react-native'
import { ScrollView } from 'react-native-gesture-handler'
import { Spinner, TextDefault } from '../../components'
import { colors, MAX_TIME } from '../../utilities'
import styles from './styles'
import { Image, Button } from 'react-native-elements'
import OrderDetails from '../../components/OrderDetails/OrderDetails'
import { OverlayComponent } from '../../components/Overlay'
import BackButton from '../../components/BackButton/BackButton'
import moment from 'moment'
import { useCancelOrder, useOrderPickedUp, useOrderRing, useRefundOrder } from '../../ui/hooks'
import CountDown from 'react-native-countdown-component'
import { useRestaurantContext } from '../../ui/context/restaurant'
import {
  ORDER_STATUS,
  canRefundOrder,
  REFUND_REASON,
  getRefundReasonText
} from '../../utilities/orderEnums'

export default function OrderDetail({ navigation, route }) {
  const { activeBar, orderData, preparationTime, createdAt } = route.params
  const { _id, orderDate } = orderData
  const { cancelOrder, loading: cancelLoading } = useCancelOrder()
  const { pickedUp, loading: loadingPicked } = useOrderPickedUp()
  const { muteRing } = useOrderRing()
  const { refundOrder, loading: refundLoading } = useRefundOrder()
  const [overlayVisible, setOverlayVisible] = useState(false)
  const [refundModalVisible, setRefundModalVisible] = useState(false)
  const isAcceptButtonVisible = !moment().isBefore(orderDate)
  const [print, setPrint] = useState(false)

  const { data } = useRestaurantContext()
  // current
  const timeNow = new Date()

  // create Time and Difference of 2 min calcualtion
  const createdTime = new Date(createdAt)
  const remainingTime = moment(createdTime)
    .add(MAX_TIME, 'seconds')
    .diff(timeNow, 'seconds')

  // accept time for late time deliveries
  const date = new Date(orderDate)
  const acceptTime = moment(date).diff(timeNow, 'seconds')

  // preparation time based on selection
  const prep = new Date(preparationTime)
  const diffTime = prep - timeNow
  const totalPrep = diffTime > 0 ? diffTime / 1000 : 0

  // checking whether to give 2 min time or accept time
  const decision = !isAcceptButtonVisible
    ? acceptTime
    : remainingTime > 0
      ? remainingTime
      : 0

  // image path
  const order = data.restaurantOrders.find(o => o._id === _id)
  const imagePath =
    activeBar === 2
      ? require('../../assets/shop.png')
      : require('../../assets/bowl.png')

  const toggleOverlay = () => {
    setPrint(false)
    setOverlayVisible(!overlayVisible)
  }
  const togglePrintOverlay = () => {
    setPrint(true)
    setOverlayVisible(!overlayVisible)
  }
  const cancelOrderFunc = () => {
    cancelOrder(order._id, 'not available')
    muteRing(order.orderId)
    if (cancelLoading) {
      return <Spinner />
    } else {
      navigation.goBack()
    }
  }
  const pickUpOrderFunc = () => {
    pickedUp(order._id)
    if (loadingPicked) {
      return <Spinner />
    } else {
      navigation.goBack()
    }
  }

  return (
    <View style={{ flex: 1 }}>
      <BackButton navigation={navigation} />

      {/* 退款模态框 */}
      <RefundModal
        visible={refundModalVisible}
        onClose={() => setRefundModalVisible(false)}
        orderData={orderData}
        onRefund={refundOrder}
        loading={refundLoading}
      />
      <ImageBackground
        source={require('../../assets/bg.png')}
        resizeMode="cover"
        style={styles.image}>
        <View style={styles.topContainer}>
          <Image
            source={require('../../assets/HeaderLight.png')}
            PlaceholderContent={<ActivityIndicator />}
            style={{ width: 150, height: 140 }}
          />
        </View>
        <View style={styles.lowerContainer}>
          <View style={styles.barContainer}>
            <View style={styles.roundedBar}>
              <View style={styles.iconContainer}>
                <Image
                  source={imagePath}
                  PlaceholderContent={<ActivityIndicator />}
                  style={{ width: 25, height: 25 }}
                />
              </View>
              <View style={styles.textContainer}>
                <TextDefault bolder H4>
                  {activeBar === 2 ? 'Prepared' : 'Preparing'}
                </TextDefault>
                <TextDefault>
                  {activeBar === 2 ? 'Delivered' : 'Accepted'}
                </TextDefault>
              </View>
            </View>
          </View>
          <ScrollView style={styles.scrollView}>
            <View style={{ alignItems: 'center', marginTop: 20 }}>
              <View style={{ marginBottom: 20 }}>
                {!isAcceptButtonVisible && (
                  <TextDefault>
                    You can accept order after the given time{' '}
                  </TextDefault>
                )}
                {activeBar === 0 && (
                  <CountDown
                    until={decision}
                    size={20}
                    timeToShow={['H', 'M', 'S']}
                    digitStyle={{ backgroundColor: colors.white }}
                    digitTxtStyle={{
                      color: 'black',
                      fontSize: 35
                    }}
                    timeLabels={{ h: null, m: null, s: null }}
                    showSeparator
                    separatorStyle={{
                      color: 'black'
                    }}
                  />
                )}
                {activeBar === 1 && (
                  <>
                    <TextDefault textColor="gray" bolder center>
                      Time Left
                    </TextDefault>
                    <CountDown
                      until={totalPrep}
                      size={20}
                      timeToShow={['H', 'M', 'S']}
                      digitStyle={{ backgroundColor: colors.white }}
                      digitTxtStyle={{
                        color: 'black',
                        fontSize: 35
                      }}
                      timeLabels={{ h: null, m: null, s: null }}
                      showSeparator
                      separatorStyle={{
                        color: 'black'
                      }}
                    />
                  </>
                )}
              </View>
              {activeBar === 0 && isAcceptButtonVisible && (
                <>
                  <Button
                    title="Accept & Print"
                    buttonStyle={{
                      backgroundColor: colors.green,
                      borderRadius: 10,
                      padding: 15
                    }}
                    titleStyle={{ color: 'black', fontWeight: '500' }}
                    containerStyle={{
                      width: 250
                    }}
                    onPress={togglePrintOverlay}
                  />

                  <Button
                    title="Accept"
                    buttonStyle={{
                      backgroundColor: 'black',
                      borderRadius: 10,
                      padding: 15
                    }}
                    titleStyle={{ color: colors.white, fontWeight: '500' }}
                    containerStyle={{
                      width: 250,
                      marginVertical: 10
                    }}
                    onPress={toggleOverlay}
                  />
                  <OverlayComponent
                    visible={overlayVisible}
                    toggle={toggleOverlay}
                    order={order}
                    print={print}
                    navigation={navigation}
                  />
                </>
              )}
              {activeBar === 1 && (
                <>
                  <Button
                    title="Delivered"
                    buttonStyle={{
                      backgroundColor: colors.green,
                      borderColor: colors.darkgreen,
                      borderWidth: 1.5,
                      borderRadius: 10,
                      padding: 15
                    }}
                    titleStyle={{
                      color: 'black',
                      fontWeight: '500'
                    }}
                    containerStyle={{
                      width: 250,
                      marginVertical: 10
                    }}
                    onPress={pickUpOrderFunc}
                  />
                </>
              )}
              {activeBar !== 2 && (
                <>
                  <Button
                    title="Reject"
                    buttonStyle={{
                      borderColor: colors.orderUncomplete,
                      borderWidth: 1.5,
                      borderRadius: 10,
                      padding: 15
                    }}
                    type="outline"
                    titleStyle={{
                      color: colors.orderUncomplete,
                      fontWeight: '500'
                    }}
                    containerStyle={{
                      width: 250
                    }}
                    onPress={cancelOrderFunc}
                  />
                </>
              )}
              {activeBar === 2 && (
                <>
                  <TextDefault H3 textColor={colors.darkgreen} bold>
                    Delivered
                  </TextDefault>
                  {/* 退款按钮 - 只在可以退款的情况下显示 */}
                  {canRefundOrder(orderData.orderStatus, orderData.refundStatus) && (
                    <Button
                      title="Refund Order"
                      buttonStyle={{
                        borderColor: colors.red,
                        borderWidth: 1.5,
                        borderRadius: 10,
                        padding: 15,
                        marginTop: 10
                      }}
                      type="outline"
                      titleStyle={{
                        color: colors.red,
                        fontWeight: '500'
                      }}
                      containerStyle={{
                        width: 250
                      }}
                      loading={refundLoading}
                      onPress={() => setRefundModalVisible(true)}
                    />
                  )}
                </>
              )}
            </View>
            <View style={styles.borderContainer}>
              <TextDefault bold H2 center>
                Order Detail
              </TextDefault>
            </View>

            <OrderDetails orderData={orderData} />
          </ScrollView>
        </View>
      </ImageBackground>
    </View>
  )
}

// 退款模态框组件
function RefundModal({ visible, onClose, orderData, onRefund, loading }) {
  const [refundAmount, setRefundAmount] = useState('')
  const [selectedReason, setSelectedReason] = useState(REFUND_REASON.MERCHANT_OTHER)
  const [reasonText, setReasonText] = useState('')

  const handleRefund = async () => {
    if (!refundAmount || parseFloat(refundAmount) <= 0) {
      Alert.alert('Error', 'Please enter a valid refund amount')
      return
    }

    if (parseFloat(refundAmount) > orderData.orderAmount) {
      Alert.alert('Error', 'Refund amount cannot exceed order amount')
      return
    }

    try {
      await onRefund(orderData._id, refundAmount, selectedReason, reasonText)
      Alert.alert('Success', 'Refund request submitted successfully')
      onClose()
      setRefundAmount('')
      setReasonText('')
    } catch (error) {
      Alert.alert('Error', 'Failed to process refund. Please try again.')
    }
  }

  const refundReasons = [
    { key: REFUND_REASON.MERCHANT_OUT_OF_STOCK, label: getRefundReasonText(REFUND_REASON.MERCHANT_OUT_OF_STOCK) },
    { key: REFUND_REASON.MERCHANT_CANNOT_DELIVER, label: getRefundReasonText(REFUND_REASON.MERCHANT_CANNOT_DELIVER) },
    { key: REFUND_REASON.MERCHANT_OTHER, label: getRefundReasonText(REFUND_REASON.MERCHANT_OTHER) },
    { key: REFUND_REASON.CUSTOMER_CANCELLED, label: getRefundReasonText(REFUND_REASON.CUSTOMER_CANCELLED) }
  ]

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <View style={{
          backgroundColor: 'white',
          borderRadius: 10,
          padding: 20,
          width: '90%',
          maxHeight: '80%'
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            marginBottom: 20,
            textAlign: 'center'
          }}>
            Refund Order
          </Text>

          <Text style={{ fontSize: 16, marginBottom: 10 }}>
            Order Amount: ${orderData.orderAmount}
          </Text>

          <Text style={{ fontSize: 14, marginBottom: 5 }}>Refund Amount:</Text>
          <TextInput
            style={{
              borderWidth: 1,
              borderColor: '#ccc',
              borderRadius: 5,
              padding: 10,
              marginBottom: 15,
              fontSize: 16
            }}
            value={refundAmount}
            onChangeText={setRefundAmount}
            placeholder="Enter refund amount"
            keyboardType="numeric"
          />

          <Text style={{ fontSize: 14, marginBottom: 5 }}>Reason:</Text>
          <ScrollView style={{ maxHeight: 120, marginBottom: 15 }}>
            {refundReasons.map((reason) => (
              <Button
                key={reason.key}
                title={reason.label}
                type={selectedReason === reason.key ? "solid" : "outline"}
                buttonStyle={{
                  marginBottom: 5,
                  backgroundColor: selectedReason === reason.key ? colors.blue : 'transparent',
                  borderColor: colors.blue
                }}
                titleStyle={{
                  color: selectedReason === reason.key ? 'white' : colors.blue,
                  fontSize: 12
                }}
                onPress={() => setSelectedReason(reason.key)}
              />
            ))}
          </ScrollView>

          <Text style={{ fontSize: 14, marginBottom: 5 }}>Additional Notes (Optional):</Text>
          <TextInput
            style={{
              borderWidth: 1,
              borderColor: '#ccc',
              borderRadius: 5,
              padding: 10,
              marginBottom: 20,
              height: 60,
              textAlignVertical: 'top'
            }}
            value={reasonText}
            onChangeText={setReasonText}
            placeholder="Enter additional details..."
            multiline
          />

          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Button
              title="Cancel"
              type="outline"
              buttonStyle={{
                borderColor: colors.fontSecondColor,
                width: 120
              }}
              titleStyle={{ color: colors.fontSecondColor }}
              onPress={onClose}
            />
            <Button
              title="Process Refund"
              buttonStyle={{
                backgroundColor: colors.red,
                width: 150
              }}
              loading={loading}
              onPress={handleRefund}
            />
          </View>
        </View>
      </View>
    </Modal>
  )
}
