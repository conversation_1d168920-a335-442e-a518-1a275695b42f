/**
 * GraphQL查询和变更验证工具
 * 用于验证退款相关的GraphQL操作是否正确定义
 */

import { orders, getRefund, getOrderRefunds } from '../apollo/queries'
import { refundOrder } from '../apollo/mutations'

/**
 * 验证GraphQL查询字符串的基本结构
 * @param {string} query - GraphQL查询字符串
 * @param {string} operationType - 操作类型 ('query' | 'mutation' | 'subscription')
 * @returns {Object} 验证结果
 */
function validateGraphQLStructure(query, operationType) {
  const errors = []
  const warnings = []

  if (!query || typeof query !== 'string') {
    errors.push('Query must be a non-empty string')
    return { isValid: false, errors, warnings }
  }

  // 检查基本结构
  if (!query.includes(operationType)) {
    errors.push(`Query should contain ${operationType} keyword`)
  }

  // 检查括号匹配
  const openBraces = (query.match(/{/g) || []).length
  const closeBraces = (query.match(/}/g) || []).length
  if (openBraces !== closeBraces) {
    errors.push('Mismatched braces in GraphQL query')
  }

  const openParens = (query.match(/\(/g) || []).length
  const closeParens = (query.match(/\)/g) || []).length
  if (openParens !== closeParens) {
    errors.push('Mismatched parentheses in GraphQL query')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证订单查询是否包含退款字段
 * @returns {Object} 验证结果
 */
export function validateOrdersQuery() {
  console.log('🔍 Validating orders query...')
  
  const result = validateGraphQLStructure(orders, 'query')
  
  // 检查退款相关字段
  const requiredRefundFields = [
    'refunds',
    'totalRefunded',
    'refundStatus'
  ]
  
  const missingFields = requiredRefundFields.filter(field => !orders.includes(field))
  
  if (missingFields.length > 0) {
    result.errors.push(`Missing refund fields: ${missingFields.join(', ')}`)
    result.isValid = false
  }

  // 检查退款对象的字段
  const requiredRefundObjectFields = [
    'refundId',
    'refundType',
    'requestAmount',
    'finalRefundAmount',
    'reason',
    'status'
  ]

  const missingRefundFields = requiredRefundObjectFields.filter(field => !orders.includes(field))
  
  if (missingRefundFields.length > 0) {
    result.warnings.push(`Missing refund object fields: ${missingRefundFields.join(', ')}`)
  }

  console.log('Orders query validation:', result)
  return result
}

/**
 * 验证退款查询
 * @returns {Object} 验证结果
 */
export function validateRefundQueries() {
  console.log('🔍 Validating refund queries...')
  
  const getRefundResult = validateGraphQLStructure(getRefund, 'query')
  const getOrderRefundsResult = validateGraphQLStructure(getOrderRefunds, 'query')
  
  // 检查参数
  if (!getRefund.includes('$refundId: String!')) {
    getRefundResult.errors.push('getRefund query missing required refundId parameter')
    getRefundResult.isValid = false
  }

  if (!getOrderRefunds.includes('$orderId: String!')) {
    getOrderRefundsResult.errors.push('getOrderRefunds query missing required orderId parameter')
    getOrderRefundsResult.isValid = false
  }

  console.log('getRefund validation:', getRefundResult)
  console.log('getOrderRefunds validation:', getOrderRefundsResult)
  
  return {
    getRefund: getRefundResult,
    getOrderRefunds: getOrderRefundsResult,
    allValid: getRefundResult.isValid && getOrderRefundsResult.isValid
  }
}

/**
 * 验证退款变更操作
 * @returns {Object} 验证结果
 */
export function validateRefundMutation() {
  console.log('🔍 Validating refund mutation...')
  
  const result = validateGraphQLStructure(refundOrder, 'mutation')
  
  // 检查必需参数
  const requiredParams = [
    '$_id: String!',
    '$amount: Float!',
    '$reason: RefundReason!',
    '$reasonText: String'
  ]

  const missingParams = requiredParams.filter(param => !refundOrder.includes(param))
  
  if (missingParams.length > 0) {
    result.errors.push(`Missing required parameters: ${missingParams.join(', ')}`)
    result.isValid = false
  }

  // 检查返回字段
  const requiredReturnFields = [
    'success',
    'refund',
    'order',
    'message'
  ]

  const missingReturnFields = requiredReturnFields.filter(field => !refundOrder.includes(field))
  
  if (missingReturnFields.length > 0) {
    result.errors.push(`Missing return fields: ${missingReturnFields.join(', ')}`)
    result.isValid = false
  }

  console.log('Refund mutation validation:', result)
  return result
}

/**
 * 运行所有GraphQL验证测试
 * @returns {Object} 测试结果
 */
export function runGraphQLValidationTests() {
  console.log('🧪 Running GraphQL validation tests...')
  
  const ordersResult = validateOrdersQuery()
  const queriesResult = validateRefundQueries()
  const mutationResult = validateRefundMutation()
  
  const allValid = ordersResult.isValid && queriesResult.allValid && mutationResult.isValid
  
  console.log(allValid ? '✅ All GraphQL tests passed!' : '❌ Some GraphQL tests failed!')
  
  return {
    allValid,
    orders: ordersResult,
    queries: queriesResult,
    mutation: mutationResult
  }
}

/**
 * 检查GraphQL查询的字段覆盖率
 * @returns {Object} 覆盖率报告
 */
export function checkFieldCoverage() {
  console.log('📊 Checking GraphQL field coverage...')
  
  // 根据服务器端定义检查字段覆盖率
  const serverRefundFields = [
    '_id', 'refundId', 'orderId', 'refundType', 'requestAmount', 
    'finalRefundAmount', 'reason', 'reasonText', 'feeBearer', 
    'transactionFee', 'status', 'stripeRefundId', 'createdAt', 
    'processedAt', 'completedAt', 'errorMessage'
  ]
  
  const serverOrderFields = [
    'refunds', 'totalRefunded', 'refundStatus'
  ]
  
  const coveredRefundFields = serverRefundFields.filter(field => orders.includes(field))
  const coveredOrderFields = serverOrderFields.filter(field => orders.includes(field))
  
  const refundCoverage = (coveredRefundFields.length / serverRefundFields.length) * 100
  const orderCoverage = (coveredOrderFields.length / serverOrderFields.length) * 100
  
  console.log(`📈 Refund fields coverage: ${refundCoverage.toFixed(1)}% (${coveredRefundFields.length}/${serverRefundFields.length})`)
  console.log(`📈 Order refund fields coverage: ${orderCoverage.toFixed(1)}% (${coveredOrderFields.length}/${serverOrderFields.length})`)
  
  return {
    refundCoverage,
    orderCoverage,
    coveredRefundFields,
    coveredOrderFields,
    missingRefundFields: serverRefundFields.filter(field => !orders.includes(field)),
    missingOrderFields: serverOrderFields.filter(field => !orders.includes(field))
  }
}

// 如果直接运行此文件，执行测试
if (typeof require !== 'undefined' && require.main === module) {
  runGraphQLValidationTests()
  checkFieldCoverage()
}
