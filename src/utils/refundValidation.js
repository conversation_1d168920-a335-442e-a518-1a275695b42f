/**
 * 退款功能验证工具
 * 用于验证退款相关的数据结构和逻辑
 */

import { 
  ORDER_STATUS, 
  REFUND_REASON, 
  REFUND_TYPE, 
  REFUND_STATUS, 
  ORDER_REFUND_STATUS,
  canRefundOrder,
  canPartialRefund,
  getOrderStatusText,
  getRefundReasonText,
  getRefundStatusText
} from '../utilities/orderEnums'

/**
 * 验证订单数据结构是否包含退款相关字段
 * @param {Object} order - 订单对象
 * @returns {Object} 验证结果
 */
export function validateOrderStructure(order) {
  const errors = []
  const warnings = []

  // 检查必需字段
  if (!order) {
    errors.push('Order object is null or undefined')
    return { isValid: false, errors, warnings }
  }

  // 检查基本字段
  if (!order._id) errors.push('Missing order._id')
  if (!order.orderId) errors.push('Missing order.orderId')
  if (typeof order.orderAmount !== 'number') errors.push('Missing or invalid order.orderAmount')
  if (!order.orderStatus) errors.push('Missing order.orderStatus')

  // 检查退款相关字段
  if (order.refunds && !Array.isArray(order.refunds)) {
    errors.push('order.refunds should be an array')
  }

  if (order.totalRefunded !== undefined && typeof order.totalRefunded !== 'number') {
    errors.push('order.totalRefunded should be a number')
  }

  if (order.refundStatus && !Object.values(ORDER_REFUND_STATUS).includes(order.refundStatus)) {
    errors.push(`Invalid refundStatus: ${order.refundStatus}`)
  }

  // 检查退款记录结构
  if (order.refunds && order.refunds.length > 0) {
    order.refunds.forEach((refund, index) => {
      if (!refund._id) warnings.push(`Refund ${index} missing _id`)
      if (!refund.refundId) warnings.push(`Refund ${index} missing refundId`)
      if (typeof refund.requestAmount !== 'number') warnings.push(`Refund ${index} missing requestAmount`)
      if (typeof refund.finalRefundAmount !== 'number') warnings.push(`Refund ${index} missing finalRefundAmount`)
      if (!Object.values(REFUND_REASON).includes(refund.reason)) {
        warnings.push(`Refund ${index} has invalid reason: ${refund.reason}`)
      }
      if (!Object.values(REFUND_STATUS).includes(refund.status)) {
        warnings.push(`Refund ${index} has invalid status: ${refund.status}`)
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证退款逻辑
 * @param {Object} order - 订单对象
 * @returns {Object} 验证结果
 */
export function validateRefundLogic(order) {
  const errors = []
  const warnings = []

  if (!order) {
    errors.push('Order object is required')
    return { isValid: false, errors, warnings }
  }

  // 检查退款状态一致性
  const hasRefunds = order.refunds && order.refunds.length > 0
  const totalRefunded = order.totalRefunded || 0
  const refundStatus = order.refundStatus || ORDER_REFUND_STATUS.NONE

  if (hasRefunds && refundStatus === ORDER_REFUND_STATUS.NONE) {
    errors.push('Order has refunds but refundStatus is NONE')
  }

  if (!hasRefunds && refundStatus !== ORDER_REFUND_STATUS.NONE) {
    errors.push('Order has no refunds but refundStatus is not NONE')
  }

  if (totalRefunded > 0 && refundStatus === ORDER_REFUND_STATUS.NONE) {
    errors.push('Order has totalRefunded > 0 but refundStatus is NONE')
  }

  if (totalRefunded === 0 && refundStatus !== ORDER_REFUND_STATUS.NONE) {
    warnings.push('Order has refundStatus but totalRefunded is 0')
  }

  // 检查退款金额
  if (hasRefunds) {
    const calculatedTotal = order.refunds.reduce((sum, refund) => sum + refund.finalRefundAmount, 0)
    if (Math.abs(calculatedTotal - totalRefunded) > 0.01) {
      errors.push(`Calculated refund total (${calculatedTotal}) doesn't match totalRefunded (${totalRefunded})`)
    }
  }

  if (totalRefunded > order.orderAmount) {
    errors.push('Total refunded amount exceeds order amount')
  }

  // 检查退款状态逻辑
  if (totalRefunded === order.orderAmount && refundStatus !== ORDER_REFUND_STATUS.FULL) {
    warnings.push('Full refund amount but status is not FULL')
  }

  if (totalRefunded > 0 && totalRefunded < order.orderAmount && refundStatus !== ORDER_REFUND_STATUS.PARTIAL) {
    warnings.push('Partial refund amount but status is not PARTIAL')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 创建测试订单数据
 * @returns {Object} 测试订单对象
 */
export function createTestOrder() {
  return {
    _id: 'test-order-1',
    orderId: 'ORD-001',
    orderAmount: 100.00,
    orderStatus: ORDER_STATUS.DELIVERED,
    refunds: [
      {
        _id: 'refund-1',
        refundId: 'REF-001',
        orderId: 'ORD-001',
        refundType: REFUND_TYPE.PARTIAL,
        requestAmount: 30.00,
        finalRefundAmount: 30.00,
        reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK,
        reasonText: 'Item was out of stock',
        feeBearer: 'merchant',
        transactionFee: 1.50,
        status: REFUND_STATUS.SUCCEEDED,
        createdAt: new Date().toISOString()
      }
    ],
    totalRefunded: 30.00,
    refundStatus: ORDER_REFUND_STATUS.PARTIAL
  }
}

/**
 * 运行所有验证测试
 * @returns {Object} 测试结果
 */
export function runValidationTests() {
  console.log('🧪 Running refund validation tests...')
  
  const testOrder = createTestOrder()
  const structureResult = validateOrderStructure(testOrder)
  const logicResult = validateRefundLogic(testOrder)
  
  console.log('📋 Structure validation:', structureResult)
  console.log('🔍 Logic validation:', logicResult)
  
  // 测试枚举函数
  console.log('🏷️ Testing enum functions:')
  console.log('- Order status text:', getOrderStatusText(ORDER_STATUS.DELIVERED))
  console.log('- Refund reason text:', getRefundReasonText(REFUND_REASON.MERCHANT_OUT_OF_STOCK))
  console.log('- Refund status text:', getRefundStatusText(REFUND_STATUS.SUCCEEDED))
  
  // 测试退款权限检查
  console.log('🔐 Testing refund permissions:')
  console.log('- Can refund delivered order:', canRefundOrder(ORDER_STATUS.DELIVERED, ORDER_REFUND_STATUS.NONE))
  console.log('- Can refund pending order:', canRefundOrder(ORDER_STATUS.PENDING, ORDER_REFUND_STATUS.NONE))
  console.log('- Can partial refund:', canPartialRefund(ORDER_STATUS.DELIVERED, ORDER_REFUND_STATUS.NONE))
  
  const allValid = structureResult.isValid && logicResult.isValid
  
  console.log(allValid ? '✅ All tests passed!' : '❌ Some tests failed!')
  
  return {
    allValid,
    structureResult,
    logicResult
  }
}

// 如果直接运行此文件，执行测试
if (typeof require !== 'undefined' && require.main === module) {
  runValidationTests()
}
