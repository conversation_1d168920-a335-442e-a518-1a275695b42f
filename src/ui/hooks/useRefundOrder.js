import { useMutation, gql } from '@apollo/client'
import { refundOrder, orders } from '../../apollo'

const REFUND_ORDER = gql`
  ${refundOrder}
`

const ORDERS = gql`
  ${orders}
`

export default function useRefundOrder() {
  const [mutateRefund, { loading, error, data }] = useMutation(
    REFUND_ORDER,
    {
      refetchQueries: [ORDERS],
      onCompleted: (data) => {
        if (data?.refundOrder?.success) {
          console.log('Refund successful:', data.refundOrder.message)
        }
      },
      onError: (error) => {
        console.error('Refund error:', error.message)
      }
    }
  )

  const refundOrderFunc = (_id, amount, reason, reasonText = '') => {
    return mutateRefund({ 
      variables: { 
        _id, 
        amount: parseFloat(amount), 
        reason, 
        reasonText 
      } 
    })
  }

  return { 
    loading, 
    error, 
    data,
    refundOrder: refundOrderFunc 
  }
}
