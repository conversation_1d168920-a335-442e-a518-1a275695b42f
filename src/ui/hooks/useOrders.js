import { useState, useContext } from 'react'
import { Restaurant } from '../context'
import {
  ORDER_STATUS,
  getProcessingOrderStatuses,
  getCompletedOrderStatuses,
  getRefundedOrderStatuses
} from '../../utilities/orderEnums'

export default function useOrders() {
  const [active, setActive] = useState(0)

  const { loading, error, data, refetch, networkStatus } = useContext(
    Restaurant.Context
  )
  const activeOrders =
    data &&
    data.restaurantOrders.filter(order => order.orderStatus === ORDER_STATUS.PENDING)
      .length
  const processingOrders =
    data &&
    data.restaurantOrders.filter(order =>
      getProcessingOrderStatuses().includes(order.orderStatus)
    ).length
  const deliveredOrders =
    data &&
    data.restaurantOrders.filter(order =>
      getCompletedOrderStatuses().includes(order.orderStatus)
    ).length
  const refundedOrders =
    data &&
    data.restaurantOrders.filter(order =>
      getRefundedOrderStatuses().includes(order.orderStatus)
    ).length

  return {
    loading,
    error,
    data,
    refetch,
    networkStatus,
    activeOrders,
    processingOrders,
    deliveredOrders,
    refundedOrders,
    active,
    setActive
  }
}
