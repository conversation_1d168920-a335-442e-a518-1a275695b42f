import { useState, useCallback, useContext } from 'react'
import { Alert, Platform } from 'react-native'
import {
  printAsync,
  printToInnerPrinter,
  selectPrinterAsync
} from '../../utilities/print'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Configuration, Restaurant } from '../context'
import useOrders from './useOrders'

const usePrintOrder = () => {
  const [isLoading, setIsLoading] = useState(false)
  const { currencySymbol } = useContext(Configuration.Context)
  const {
    printer,
    setPrinter,
    useInnerPrinter,
    toggleInnerPrinter
  } = useContext(Restaurant.Context)
  const { loading, error, data } = useOrders()

  const printOrder = useCallback(
    async orderId => {
      if (loading || error || !orderId) return false

      setIsLoading(true)
      try {
        let success = false

        // Find the order by ID
        const order = data.restaurantOrders.find(order => order._id === orderId)
        if (!order) {
          console.log('Order not found:', orderId)
          setIsLoading(false)
          return false
        }

        // Add currency symbol to order
        const orderWithCurrency = { ...order, currencySymbol }

        if (useInnerPrinter) {
          // Use InnerPrinter (Bluetooth)
          if (Platform.OS !== 'android') {
            Alert.alert('Error', 'InnerPrinter is only available on Android')
            setIsLoading(false)
            return false
          }

          success = await printToInnerPrinter(orderWithCurrency)

          if (!success) {
            Alert.alert(
              'Print Error',
              'Failed to print to InnerPrinter. Please make sure the printer is connected and try again.'
            )
          }
        } else {
          // Use standard printer
          success = await printAsync(
            orderWithCurrency,
            Platform.OS === 'ios' ? (printer ? printer.url : null) : null
          )

          if (!success) {
            Alert.alert('Print Error', 'Failed to print order')
          }
        }

        console.log('Print result:', success)
        setIsLoading(false)
        return success
      } catch (error) {
        console.error('Error printing order:', error)
        Alert.alert(
          'Print Error',
          error.message || 'An error occurred while printing'
        )
        setIsLoading(false)
        return false
      }
    },
    [printer, useInnerPrinter, loading, error, data, currencySymbol]
  )

  const selectPrinter = useCallback(async () => {
    try {
      const result = await selectPrinterAsync()
      if (result) {
        setPrinter(result)
        await AsyncStorage.setItem('printer', JSON.stringify(result))
      }
      console.log('Selected printer:', result)
      return result
    } catch (error) {
      console.error('Error selecting printer:', error)
      Alert.alert(
        'Printer Error',
        error.message || 'An error occurred while selecting printer'
      )
      return null
    }
  }, [setPrinter])

  return {
    isLoading,
    printer,
    useInnerPrinter,
    printOrder,
    selectPrinter,
    toggleInnerPrinter
  }
}
export default usePrintOrder
