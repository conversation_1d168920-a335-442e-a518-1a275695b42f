import React, { useContext, useEffect, useState } from 'react'
import { useQuery, gql } from '@apollo/client'
import { subscribePlaceOrder, orders } from '../../apollo'
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as SecureStore from 'expo-secure-store'
import { Alert, Platform } from 'react-native'
import BluetoothUtil from '../../utilities/BluetoothUtil'

const Context = React.createContext({})
const Provider = props => {
  const [printer, setPrinter] = useState()
  const [notificationToken, setNotificationToken] = useState()
  const [useInnerPrinter, setUseInnerPrinter] = useState(true)

  useEffect(() => {
    ;(async () => {
      const printerStr = await AsyncStorage.getItem('printer')
      if (printerStr) setPrinter(JSON.parse(printerStr))

      // Load useInnerPrinter setting from AsyncStorage
      const useInnerPrinterStr = await AsyncStorage.getItem('useInnerPrinter')
      if (useInnerPrinterStr !== null) {
        setUseInnerPrinter(JSON.parse(useInnerPrinterStr))
      } else {
        // If not set yet, default to true and save it
        await AsyncStorage.setItem('useInnerPrinter', JSON.stringify(true))
      }
    })()
  }, [])

  const toggleInnerPrinter = async () => {
    try {
      if (Platform.OS !== 'android') {
        Alert.alert(
          'Not Available',
          'InnerPrinter is only available on Android'
        )
        return
      }

      // If turning on InnerPrinter
      if (!useInnerPrinter) {
        // Request Bluetooth permissions
        const permissionsGranted = await BluetoothUtil.requestBluetoothPermissions()

        if (!permissionsGranted) {
          Alert.alert(
            'Permission Denied',
            'Bluetooth permissions are required to use the InnerPrinter'
          )
          return
        }

        // Check if Bluetooth is available
        const isAvailable = await BluetoothUtil.isBluetoothAvailable()

        if (!isAvailable) {
          // Try to enable Bluetooth
          const enabled = await BluetoothUtil.enableBluetooth()

          if (!enabled) {
            Alert.alert(
              'Bluetooth Required',
              'Please enable Bluetooth to use the InnerPrinter'
            )
            return
          }
        }

        // Find InnerPrinter device
        const device = await BluetoothUtil.findInnerPrinter()

        if (!device) {
          Alert.alert(
            'Printer Not Found',
            'Could not find the InnerPrinter. Please make sure it is paired with your device.'
          )
          return
        }

        // Successfully found the printer, enable InnerPrinter mode
        setUseInnerPrinter(true)
        await AsyncStorage.setItem('useInnerPrinter', JSON.stringify(true))
        Alert.alert('Success', 'InnerPrinter is now enabled')
      } else {
        // Turning off InnerPrinter
        setUseInnerPrinter(false)
        await AsyncStorage.setItem('useInnerPrinter', JSON.stringify(false))
        Alert.alert('Success', 'InnerPrinter is now disabled')
      }
    } catch (error) {
      console.error('Error toggling InnerPrinter:', error)
      Alert.alert(
        'Error',
        error.message || 'An error occurred while toggling InnerPrinter'
      )
    }
  }

  const {
    loading,
    error,
    data,
    subscribeToMore,
    refetch,
    networkStatus
  } = useQuery(
    gql`
      ${orders}
    `,
    { fetchPolicy: 'network-only', pollInterval: 15000, onError }
  )
  function onError(error) {
    console.log(JSON.stringify(error))
  }
  let unsubscribe = null
  useEffect(() => {
    return () => {
      unsubscribe && unsubscribe()
    }
  }, [])

  useEffect(() => {
    subscribeToMoreOrders()
  }, [])

  useEffect(() => {
    async function GetToken() {
      const result = await SecureStore.getItemAsync('notification-token')
      if (result) {
        setNotificationToken(JSON.parse(result))
      } else {
        setNotificationToken(null)
      }
    }
    GetToken()
  }, [])

  const subscribeToMoreOrders = async () => {
    const restaurant = await AsyncStorage.getItem('restaurantId')
    if (!restaurant) return
    unsubscribe = subscribeToMore({
      document: gql`
        ${subscribePlaceOrder}
      `,
      variables: { restaurant },
      updateQuery: (prev, { subscriptionData }) => {
        if (!subscriptionData.data) return prev
        const { restaurantOrders } = prev
        const { origin, order } = subscriptionData.data.subscribePlaceOrder
        if (origin === 'new') {
          return {
            restaurantOrders: [order, ...restaurantOrders]
          }
        }
        return prev
      },
      onError: error => {
        console.log('onError', error)
      }
    })
  }

  return (
    <Context.Provider
      value={{
        loading,
        error,
        data,
        subscribeToMoreOrders,
        refetch,
        networkStatus,
        printer,
        setPrinter,
        notificationToken,
        useInnerPrinter,
        toggleInnerPrinter
      }}>
      {props.children}
    </Context.Provider>
  )
}
export const useRestaurantContext = () => useContext(Context)
export default { Context, Provider }
