import Constants from 'expo-constants'

export default {
  levels: {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    CRITICAL: 4
  },
  envStrategies: {
    development: {
      level: 'DEBUG',
      transports: ['console', 'file'],
      apolloLog: 'verbose'
    },
    production: {
      level: 'WARN',
      transports: ['file', 'sentry'],
      apolloLog: 'errors-only'
    }
  },
  getCurrentConfig() {
    const env = __DEV__
      ? 'development'
      : Constants.manifest.releaseChannel || 'production'
    return this.envStrategies[env]
  },
  applyRemoteConfig(remoteConfig) {
    return Object.assign(this.envStrategies, remoteConfig)
  }
}
