class Logger {
  constructor(namespace) {
    this.namespace = namespace
    this.levelColors = {
      DEBUG: '\x1b[36m', // Cyan
      INFO: '\x1b[32m', // Green
      WARN: '\x1b[33m', // Yellow
      ERROR: '\x1b[31m' // Red
    }
    this.reset = '\x1b[0m'
  }

  _log(level, message, data = {}) {
    const color = this.levelColors[level] || ''
    const timestamp = new Date().toISOString()
    const dataString = Object.keys(data).length ? JSON.stringify(data) : ''

    const logMessage = `${timestamp} ${color}[${level}]${this.reset} [${this.namespace}] ${message} ${dataString}`

    switch (level) {
      case 'ERROR':
        console.error(logMessage)
        break
      case 'WARN':
        console.warn(logMessage)
        break
      case 'INFO':
        console.info(logMessage)
        break
      default:
        console.log(logMessage)
    }
  }

  debug(message, data) {
    this._log('DEBUG', message, data)
  }

  info(message, data) {
    this._log('INFO', message, data)
  }

  warn(message, data) {
    this._log('WARN', message, data)
  }

  error(message, data) {
    this._log('ERROR', message, data)
  }
}

export default Logger
