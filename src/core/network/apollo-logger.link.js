import { ApolloLink } from '@apollo/client'
import Logger from '../logger'
import { Observable } from '@apollo/client/utilities' // 使用 Apollo 的 Observable

class ApolloLoggerLink extends ApolloLink {
  constructor(url) {
    super()
    this.logger = new Logger('Apollo')
    this.url = url
  }

  request(operation, forward) {
    const startTime = Date.now()
    const operationName = operation.operationName || 'unnamed'

    this.logger.debug(`Operation started: ${operationName}`, {
      variables: operation.variables,
      uri: this.url
    })

    const forwardResult = forward(operation)
    if (!forwardResult) {
      return new Observable(observer => {
        observer.error(
          new Error('forward(operation) returned null or undefined')
        )
      })
    }

    return new Observable(observer => {
      const subscription = forwardResult.subscribe({
        next: response => {
          const duration = Date.now() - startTime

          if (response.errors) {
            this.logger.error(`Operation failed: ${operationName}`, {
              errors: response.errors,
              duration,
              uri: this.urlr
            })
          } else {
            this.logger.info(`Operation completed: ${operationName}`, {
              duration,
              data: response.data,
              uri: this.url
            })
          }

          observer.next(response)
        },
        error: error => {
          this.logger.error(`Operation error: ${operationName}`, {
            error,
            uri: this.url
          })
          observer.error(error)
        },
        complete: () => {
          observer.complete()
        }
      })

      // 清理函数
      return () => {
        if (subscription) {
          subscription.unsubscribe()
        }
      }
    })
  }
}

export default ApolloLoggerLink
