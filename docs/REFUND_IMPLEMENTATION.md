# 退款功能实现文档

## 概述

本文档描述了为Firespoon Restaurant应用添加的退款功能实现。该实现基于最新的服务器端GraphQL定义，支持完整的退款流程管理。

## 主要变更

### 1. 枚举定义 (`src/utilities/orderEnums.js`)

新增了完整的订单和退款相关枚举：

- **ORDER_STATUS**: 扩展了订单状态，包括 `PARTIALLY_REFUNDED` 和 `REFUNDED`
- **REFUND_REASON**: 退款原因枚举
- **REFUND_TYPE**: 退款类型（全额/部分）
- **REFUND_STATUS**: 退款处理状态
- **ORDER_REFUND_STATUS**: 订单退款状态

### 2. GraphQL 更新

#### 查询 (`src/apollo/queries.js`)
- 更新 `orders` 查询，添加退款相关字段：
  - `refunds`: 退款记录数组
  - `totalRefunded`: 总退款金额
  - `refundStatus`: 订单退款状态
- 新增 `getRefund` 和 `getOrderRefunds` 查询

#### 变更 (`src/apollo/mutations.js`)
- 新增 `refundOrder` mutation，支持退款操作

### 3. Hooks 更新

#### 现有Hooks更新
- **useOrders.js**: 添加对退款订单的统计和过滤
- **useOrderStatus.js**: 处理退款状态的订单状态变更

#### 新增Hooks
- **useRefundOrder.js**: 处理退款操作的专用Hook

### 4. UI组件更新

#### 订单详情组件
- **OrderDetails.js**: 添加退款信息显示，包括退款历史
- **OrderDetail.js**: 添加退款操作按钮和退款模态框

#### 订单列表组件
- **HomeOrderDetails.js**: 显示订单的退款状态徽章
- **Orders.js**: 添加退款订单的筛选和显示
- **TabBars.js**: 新增"Refunded"标签页

#### 新增UI组件
- **RefundStatusBadge.js**: 退款状态徽章组件
- **RefundHistoryList.js**: 退款历史列表组件

## 功能特性

### 1. 退款状态管理
- 支持部分退款和全额退款
- 实时显示退款状态和金额
- 退款历史记录追踪

### 2. 退款操作
- 直观的退款界面
- 退款原因选择
- 退款金额输入和验证
- 退款备注功能

### 3. 订单状态更新
- 自动更新订单状态
- 支持新的退款相关状态
- 实时状态同步

### 4. 用户界面
- 退款状态徽章显示
- 退款订单专用标签页
- 详细的退款历史展示

## 使用方法

### 1. 查看退款信息
- 在订单列表中，退款订单会显示红色的退款状态徽章
- 点击订单进入详情页面查看完整的退款历史

### 2. 执行退款操作
1. 进入已交付订单的详情页面
2. 点击"Refund Order"按钮
3. 选择退款原因
4. 输入退款金额
5. 添加备注（可选）
6. 确认退款

### 3. 管理退款订单
- 在订单页面点击"Refunded"标签查看所有退款订单
- 退款订单按退款状态分类显示

## 数据结构

### 订单对象扩展
```javascript
{
  // 现有字段...
  refunds: [
    {
      _id: String,
      refundId: String,
      orderId: String,
      refundType: 'FULL' | 'PARTIAL',
      requestAmount: Number,
      finalRefundAmount: Number,
      reason: RefundReason,
      reasonText: String,
      feeBearer: String,
      transactionFee: Number,
      status: RefundStatus,
      stripeRefundId: String,
      createdAt: DateTime,
      processedAt: DateTime,
      completedAt: DateTime,
      errorMessage: String
    }
  ],
  totalRefunded: Number,
  refundStatus: 'NONE' | 'PARTIAL' | 'FULL'
}
```

## 验证和测试

项目包含了完整的验证工具：

- `src/utils/refundValidation.js`: 数据结构和业务逻辑验证
- `src/utils/graphqlValidation.js`: GraphQL查询和变更验证
- `src/utils/validateRefundImplementation.js`: 综合验证脚本

运行验证：
```javascript
import { runCompleteValidation } from './src/utils/validateRefundImplementation'
runCompleteValidation()
```

## 注意事项

1. **权限控制**: 只有已交付或已完成的订单才能进行退款
2. **金额验证**: 退款金额不能超过订单总金额
3. **状态一致性**: 退款状态与退款记录保持一致
4. **错误处理**: 完善的错误提示和异常处理

## 兼容性

- 向后兼容现有的订单数据结构
- 新字段使用默认值确保兼容性
- 渐进式功能启用

## 后续改进

1. 添加退款通知功能
2. 支持批量退款操作
3. 退款报表和统计
4. 退款审批流程
5. 自动退款规则

## 技术栈

- React Native
- Apollo GraphQL Client
- React Navigation
- React Native Elements

## 维护说明

- 定期运行验证脚本确保数据一致性
- 监控退款操作的成功率
- 及时更新枚举定义以匹配服务器端变更
