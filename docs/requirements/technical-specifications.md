# Technical Specifications Document
**Project**: Firespoon Restaurant Management App  
**Version**: 1.0  
**Date**: 2025-08-24  
**Status**: Active  

## System Architecture

### Overview
Firespoon Restaurant is a React Native mobile application built with Expo framework, designed for restaurant order management. The application follows a client-server architecture with real-time communication capabilities.

### Technology Stack
- **Frontend Framework**: React Native 0.71.14
- **Development Platform**: Expo SDK 48.0.21
- **JavaScript Engine**: Hermes
- **State Management**: Apollo Client with GraphQL
- **Navigation**: React Navigation 6.x
- **UI Components**: React Native Elements
- **Real-time Communication**: GraphQL Subscriptions over WebSocket
- **Error Monitoring**: Sentry
- **Build System**: EAS Build
- **Package Manager**: npm

### Architecture Layers

#### 1. Presentation Layer (`src/screens`, `src/components`)
- **Screens**: Login, Orders, OrderDetail
- **Components**: Reusable UI components (Spinner, TextDefault, etc.)
- **Navigation**: Stack, Tab, and Drawer navigation patterns

#### 2. Business Logic Layer (`src/ui/hooks`, `src/utilities`)
- **Custom Hooks**: useOrders, useAcceptOrder for state management
- **Utilities**: Color schemes, text styles, scaling, alignment
- **Validation**: Form validation using validate.js

#### 3. Data Access Layer (`src/apollo`)
- **GraphQL Client**: Apollo Client configuration
- **Queries**: Restaurant orders, configuration, restaurant info
- **Mutations**: Login, order management (accept, cancel, pickup)
- **Subscriptions**: Real-time order updates

#### 4. Core Services Layer (`src/core`)
- **Logger**: Multi-level logging with environment-specific strategies
- **Network**: Apollo logger link for request/response monitoring
- **Security**: Token-based authentication with Expo SecureStore

#### 5. Platform Integration Layer (`src/utilities`)
- **Bluetooth**: Printer connection and communication
- **Notifications**: Push notification handling
- **Print**: Receipt formatting and printing

### External Dependencies
- **Backend API**: GraphQL endpoint for data operations
- **WebSocket**: Real-time subscription endpoint
- **Bluetooth Printers**: Thermal printer integration
- **Push Notification Service**: Expo notification service

## Data Design

### Data Models

#### Order Model
```javascript
{
  _id: String,
  orderId: String,
  orderAmount: Number,
  restaurantId: String,
  restaurantName: String,
  orderStatus: Enum['PENDING', 'ACCEPTED', 'DELIVERED', 'CANCELLED'],
  customerPhone: String,
  deliveryAddress: String,
  deliveryInstructions: String,
  items: [OrderItem],
  paymentMethod: String,
  paidAmount: Number,
  tipping: Number,
  taxationAmount: Number,
  status: String,
  paymentStatus: String,
  reason: String,
  isActive: Boolean,
  createdAt: DateTime,
  orderDate: DateTime,
  deliveryCharges: Number,
  isPickedUp: Boolean,
  preparationTime: String,
  acceptedAt: DateTime,
  isRinged: Boolean
}
```

#### OrderItem Model
```javascript
{
  _id: String,
  title: String,
  description: String,
  image: String,
  quantity: Number,
  variation: {
    _id: String,
    title: String,
    price: Number,
    discounted: Number
  },
  addons: [{
    _id: String,
    options: [AddonOption],
    description: String,
    title: String,
    quantityMinimum: Number,
    quantityMaximum: Number
  }],
  specialInstructions: String,
  isActive: Boolean,
  createdAt: DateTime,
  updatedAt: DateTime
}
```

#### Restaurant Model
```javascript
{
  _id: String,
  orderId: Number,
  orderPrefix: String,
  name: String,
  image: String,
  address: String,
  location: {
    coordinates: [Number]
  },
  deliveryTime: Number,
  username: String,
  isAvailable: Boolean,
  notificationToken: String,
  enableNotification: Boolean,
  openingTimes: [OpeningTime]
}
```

### Data Storage Strategy
- **Local Storage**: AsyncStorage for non-sensitive data (restaurant ID, preferences)
- **Secure Storage**: Expo SecureStore for authentication tokens
- **Cache Management**: Apollo Client InMemoryCache for GraphQL data
- **Offline Support**: Limited to cached data viewing

### Data Synchronization
- **Real-time Updates**: GraphQL subscriptions for order status changes
- **Optimistic Updates**: Immediate UI updates with server confirmation
- **Conflict Resolution**: Server-side data takes precedence
- **Retry Logic**: Automatic retry for failed mutations

## API Design

### GraphQL Schema Overview

#### Queries
- `restaurantOrders`: Fetch all orders for the restaurant
- `restaurant(id: String)`: Get restaurant information
- `configuration`: Get system configuration (currency, etc.)

#### Mutations
- `restaurantLogin(username: String!, password: String!)`: Authenticate restaurant
- `acceptOrder(_id: String!, time: String)`: Accept order with preparation time
- `cancelOrder(_id: String!, reason: String!)`: Cancel order with reason
- `orderPickedUp(_id: String!)`: Mark order as picked up
- `saveRestaurantToken(token: String, isEnabled: Boolean)`: Save notification token
- `toggleAvailability`: Toggle restaurant online/offline status
- `muteRing(orderId: String)`: Mute order notification sound

#### Subscriptions
- `subscribePlaceOrder(restaurant: String!)`: Real-time new order notifications
- `subscribeOrderStatus(_id: String!)`: Real-time order status updates
- `subscriptionOrder(id: String!)`: Real-time order detail updates

### API Communication
- **HTTP Endpoint**: Configurable via environment variables
- **WebSocket Endpoint**: Separate endpoint for subscriptions
- **Authentication**: Bearer token in Authorization header
- **Error Handling**: Standardized GraphQL error responses
- **Rate Limiting**: Server-side implementation

### Environment Configuration
```javascript
// Development
GRAPHQL_URL_DEV: "http://localhost:4000/graphql"
WS_GRAPHQL_URL_DEV: "ws://localhost:4000/graphql"

// Staging  
GRAPHQL_URL_STAGING: "https://staging-api.firespoon.com/graphql"
WS_GRAPHQL_URL_STAGING: "wss://staging-api.firespoon.com/graphql"

// Production
GRAPHQL_URL_PRODUCTION: "https://api.firespoon.com/graphql"
WS_GRAPHQL_URL_PRODUCTION: "wss://api.firespoon.com/graphql"
```

## Non-Functional Requirements

### Performance Requirements
- **App Launch Time**: < 3 seconds on mid-range devices
- **Order List Load Time**: < 1 second for up to 100 orders
- **Real-time Update Latency**: < 500ms for order status changes
- **Memory Usage**: < 150MB during normal operation
- **Battery Optimization**: Background processing minimized

### Scalability Requirements
- **Concurrent Users**: Support up to 1000 restaurants simultaneously
- **Order Volume**: Handle up to 10,000 orders per restaurant per day
- **Data Pagination**: Implement pagination for large order lists
- **Cache Management**: Efficient memory usage with LRU cache eviction

### Security Requirements
- **Authentication**: JWT token-based authentication
- **Token Storage**: Secure storage using platform keychain/keystore
- **Session Management**: Automatic token refresh and expiration handling
- **Data Transmission**: HTTPS/WSS for all communications
- **Input Validation**: Client and server-side validation

### Reliability Requirements
- **Uptime**: 99.9% availability during business hours
- **Error Recovery**: Graceful handling of network failures
- **Data Consistency**: Eventual consistency with conflict resolution
- **Backup Strategy**: Real-time data replication
- **Monitoring**: Comprehensive error tracking with Sentry

### Usability Requirements
- **Accessibility**: WCAG 2.1 AA compliance
- **Internationalization**: Support for multiple languages
- **Responsive Design**: Optimized for various screen sizes
- **Offline Capability**: View cached orders when offline
- **User Feedback**: Clear loading states and error messages

### Compatibility Requirements
- **iOS**: iOS 11.0 and above
- **Android**: Android 6.0 (API level 23) and above
- **Bluetooth**: Support for ESC/POS thermal printers
- **Network**: IPv4/IPv6 support
- **Hardware**: Minimum 2GB RAM, 1GB storage

## Deployment Plan

### Build Environments

#### Development Environment
```bash
# Local development server
npm run start

# Development build
npm run build:dev
npm run build:dev:android
npm run build:dev:ios

# EAS development build
eas build --profile development
```

#### Staging Environment
```bash
# Staging build
npm run build:staging
npm run build:staging:android  
npm run build:staging:ios

# EAS staging build
eas build --profile staging -p all
```

#### Production Environment
```bash
# Production build
npm run build:production
npm run build:production:android
npm run build:production:ios

# EAS production build
eas build --profile production -p all

# App store submission
npm run submit:production
npm run submit:production:android
npm run submit:production:ios
```

### Build Methods Overview

The project supports **three different build approaches**:

1. **EAS Build (Cloud-based)** - Recommended for production
2. **Local Native Build** - Direct Android/iOS compilation
3. **Expo Development Build** - For development and testing

### Method 1: EAS Build (Cloud-based)

#### Prerequisites
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Login to Expo account
eas login

# Configure project (if not already done)
eas build:configure
```

#### Development Build
```bash
# Build for all platforms
eas build --profile development

# Platform-specific builds
eas build --profile development --platform android
eas build --profile development --platform ios
```

#### Staging Build
```bash
# Build for all platforms
eas build --profile staging

# Platform-specific builds
eas build --profile staging --platform android
eas build --profile staging --platform ios
```

#### Production Build
```bash
# Build for all platforms
eas build --profile production

# Platform-specific builds
eas build --profile production --platform android
eas build --profile production --platform ios
```

### Method 2: Local Native Build (Without Expo)

#### Prerequisites
```bash
# Install dependencies
npm install

# Apply patches
npm run postinstall

# For Android: Ensure Android SDK and tools are installed
# For iOS: Ensure Xcode and CocoaPods are installed
```

#### Android Local Build
```bash
# Navigate to android directory
cd android

# Debug build (for testing)
./gradlew assembleDebug

# Release build (for production)
./gradlew assembleRelease

# Install debug build on connected device
./gradlew installDebug

# Clean build (if needed)
./gradlew clean
```

#### iOS Local Build
```bash
# Navigate to ios directory
cd ios

# Install CocoaPods dependencies
pod install

# Build using Xcode command line
xcodebuild -workspace Firerestaurant.xcworkspace -scheme Firerestaurant -configuration Debug

# Or open in Xcode
open Firerestaurant.xcworkspace
```

### Method 3: Expo Development Build

#### Development Server
```bash
# Start development server
npm run start
# or
expo start --dev-client

# Start with specific platform
npm run android  # For Android
npm run ios      # For iOS
npm run web      # For web (if supported)
```

#### Development Client Build
```bash
# Create development client
npm run build:dev

# Platform-specific development builds
npm run build:dev:android
npm run build:dev:ios
```

### Build Configuration Files
- **EAS Build**: `eas.json` - Defines build profiles and configurations
- **App Configuration**: `app.json` - Expo app configuration and metadata
- **Environment**: `environment.js` - Environment-specific API endpoints
- **Babel**: `babel.config.js` - JavaScript transpilation rules
- **Metro**: `metro.config.js` - React Native bundler configuration
- **Android Gradle**: `android/build.gradle` - Android build configuration
- **iOS Project**: `ios/Firerestaurant.xcworkspace` - iOS build configuration

### Detailed Build Instructions

#### Step-by-Step Local Android Build

1. **Environment Setup**
```bash
# Clone the repository
git clone <repository-url>
cd Firespoon_Restaurant

# Install Node.js dependencies
npm install

# Apply necessary patches
npm run postinstall
```

2. **Android SDK Setup**
- Install Android Studio
- Install Android SDK (API level 23 or higher)
- Set ANDROID_HOME environment variable
- Add Android SDK tools to PATH

3. **Build Process**

**Debug APK (for testing):**
```bash
cd android
./gradlew assembleDebug

# Generated APK location:
# android/app/build/outputs/apk/debug/app-debug.apk
```

**Release APK (for production):**

Release builds require proper signing configuration. You have two options:

**Option A: Using existing keystore (if available)**
```bash
# Generate signed release APK
./gradlew assembleRelease

# Generated APK location:
# android/app/build/outputs/apk/release/app-release.apk
```

**Option B: Create new keystore and configure signing**

1. **Generate keystore:**
```bash
# Create new keystore (run this once)
keytool -genkeypair -v -storetype PKCS12 \
  -keystore firespoon-release-key.keystore \
  -alias firespoon-key-alias \
  -keyalg RSA \
  -keysize 2048 \
  -validity 10000

# You'll be prompted for:
# - Keystore password
# - Key password
# - Your name, organization, etc.
```

2. **Configure signing in android/app/build.gradle:**
```gradle
android {
    ...
    signingConfigs {
        release {
            if (project.hasProperty('MYAPP_RELEASE_STORE_FILE')) {
                storeFile file(MYAPP_RELEASE_STORE_FILE)
                storePassword MYAPP_RELEASE_STORE_PASSWORD
                keyAlias MYAPP_RELEASE_KEY_ALIAS
                keyPassword MYAPP_RELEASE_KEY_PASSWORD
            }
        }
    }
    buildTypes {
        release {
            ...
            signingConfig signingConfigs.release
        }
    }
}
```

3. **Create gradle.properties file in android/ directory:**
```properties
MYAPP_RELEASE_STORE_FILE=firespoon-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=firespoon-key-alias
MYAPP_RELEASE_STORE_PASSWORD=your_keystore_password
MYAPP_RELEASE_KEY_PASSWORD=your_key_password
```

4. **Build signed release APK:**
```bash
./gradlew assembleRelease
```

**Alternative: Generate unsigned release APK**
```bash
# Build release without signing (for testing)
./gradlew assembleRelease -Pandroid.injected.signing.store.file= \
                          -Pandroid.injected.signing.store.password= \
                          -Pandroid.injected.signing.key.alias= \
                          -Pandroid.injected.signing.key.password=
```

4. **Install on Device**
```bash
# Install debug build
./gradlew installDebug

# Install release build
./gradlew installRelease

# Or manually install APK using ADB
adb install app/build/outputs/apk/debug/app-debug.apk
adb install app/build/outputs/apk/release/app-release.apk

# Check connected devices
adb devices

# Uninstall previous version (if needed)
adb uninstall com.firespoon.restaurant
```

**Important Notes for Release Builds:**

- **Keystore Security**: Keep your keystore file and passwords secure. Losing them means you cannot update your app on Google Play Store.
- **Backup**: Always backup your keystore file in a secure location.
- **Environment Variables**: Never commit keystore passwords to version control.
- **ProGuard/R8**: Release builds automatically enable code obfuscation and minification.
- **App Bundle**: For Google Play Store, consider using `./gradlew bundleRelease` to generate AAB files instead of APK.

**Generate Android App Bundle (AAB) for Play Store:**
```bash
# Generate release bundle (recommended for Play Store)
./gradlew bundleRelease

# Generated AAB location:
# android/app/build/outputs/bundle/release/app-release.aab
```

#### Step-by-Step Local iOS Build

1. **Environment Setup**
```bash
# Ensure Xcode is installed (macOS only)
# Install CocoaPods
sudo gem install cocoapods

# Navigate to iOS directory
cd ios

# Install iOS dependencies
pod install
```

2. **Build Process**
```bash
# Build using Xcode command line
xcodebuild -workspace Firerestaurant.xcworkspace \
           -scheme Firerestaurant \
           -configuration Debug \
           -destination 'platform=iOS Simulator,name=iPhone 14'

# Or open in Xcode for GUI build
open Firerestaurant.xcworkspace
```

#### Build Troubleshooting

**Common Android Issues:**
```bash
# Clean build if facing issues
cd android
./gradlew clean
./gradlew assembleDebug

# Fix permission issues
chmod +x gradlew

# Clear React Native cache
npx react-native start --reset-cache
```

**Common iOS Issues:**
```bash
# Clean CocoaPods cache
cd ios
pod deintegrate
pod install

# Clean Xcode build
xcodebuild clean -workspace Firerestaurant.xcworkspace -scheme Firerestaurant
```

### Environment Variables
Required environment variables in `.env` file:
```bash
# GraphQL Endpoints
GRAPHQL_URL_DEV=http://localhost:4000/graphql
WS_GRAPHQL_URL_DEV=ws://localhost:4000/graphql
GRAPHQL_URL_STAGING=https://staging-api.firespoon.com/graphql
WS_GRAPHQL_URL_STAGING=wss://staging-api.firespoon.com/graphql
GRAPHQL_URL_PRODUCTION=https://api.firespoon.com/graphql
WS_GRAPHQL_URL_PRODUCTION=wss://api.firespoon.com/graphql

# Sentry DSN (Error Monitoring)
SENTRY_DSN_DEV=https://<EMAIL>/project-id
SENTRY_DSN_STAGING=https://<EMAIL>/project-id
SENTRY_DSN_PRODUCTION=https://<EMAIL>/project-id
```

### Build Comparison

| Method | Pros | Cons | Use Case |
|--------|------|------|----------|
| **EAS Build** | - Cloud-based, no local setup<br>- Consistent environment<br>- Easy CI/CD integration | - Requires internet<br>- Build queue wait times<br>- Expo account required | Production releases, CI/CD |
| **Local Native** | - Full control<br>- Faster iteration<br>- No internet required | - Complex setup<br>- Platform-specific tools needed<br>- Environment inconsistencies | Development, debugging |
| **Expo Dev** | - Quick setup<br>- Hot reload<br>- Easy debugging | - Limited to development<br>- Requires Expo Go or dev client | Development, testing |

### Recommended Workflow

1. **Development**: Use Expo development server (`npm run start`)
2. **Testing**: Use local native builds for device testing
3. **Staging**: Use EAS builds for QA testing
4. **Production**: Use EAS builds for app store releases

### Deployment Pipeline
1. **Code Commit**: Push to version control
2. **Automated Testing**: Run unit and integration tests
3. **Build Generation**: EAS Build creates platform-specific builds
4. **Quality Assurance**: Manual testing on staging environment
5. **Production Deployment**: Release to app stores
6. **Monitoring**: Real-time error tracking and performance monitoring

### Release Management
- **Version Control**: Semantic versioning (MAJOR.MINOR.PATCH)
- **Release Channels**: Development, Staging, Production
- **Rollback Strategy**: Immediate rollback capability via EAS Updates
- **Feature Flags**: Environment-based feature toggles
- **Documentation**: Release notes and changelog maintenance

### Infrastructure Requirements
- **EAS Build Service**: Expo Application Services for cloud builds
- **App Store Accounts**: Apple App Store and Google Play Store
- **Monitoring Services**: Sentry for error tracking
- **CDN**: Asset delivery optimization
- **SSL Certificates**: HTTPS/WSS communication security
